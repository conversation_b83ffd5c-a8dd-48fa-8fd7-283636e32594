#!/usr/bin/env python3
"""
图书馆文献查询 FastMCP 服务器
基于 FastMCP 框架实现的 MCP 服务器，提供文献检索和详情查询功能
"""

import asyncio
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP
from pydantic import BaseModel, Field

# 导入现有的服务
from app.services.auth_service import AuthService
from app.services.document_service import DocumentService
from app.models.document import DocumentSearchRequest


class SearchDocumentsArgs(BaseModel):
    """搜索文献参数"""
    query: str = Field(description="检索表达式，如：(K=Python)、(A=张三)、(K=机器学习) AND O=清华大学")
    page_size: int = Field(default=10, description="每页返回数量，默认10，最大100", ge=1, le=100)
    page_index: int = Field(default=1, description="页码，从1开始，默认1", ge=1)


class GetDocumentDetailArgs(BaseModel):
    """获取文献详情参数"""
    document_id: str = Field(description="文献的唯一标识符，如：DOC001、DOC007、DOC015")


class LibraryMCPServer:
    """图书馆 MCP 服务器"""
    
    def __init__(self):
        self.auth_service = AuthService()
        self.document_service = DocumentService()
        self._access_token: Optional[str] = None
        self._token_expires_at: Optional[float] = None
    
    def _ensure_valid_token(self) -> str:
        """确保有有效的访问令牌"""
        import time
        current_time = time.time()
        
        # 检查是否需要获取新令牌
        if (self._access_token is None or 
            self._token_expires_at is None or 
            current_time >= self._token_expires_at - 60):  # 提前60秒刷新
            
            # 获取授权码
            code = self.auth_service.generate_authorization_code("demo_app_123")
            
            # 获取访问令牌
            token_response = self.auth_service.generate_tokens(code)
            self._access_token = token_response.access_token
            self._token_expires_at = current_time + token_response.expires_in
        
        return self._access_token


# 创建 FastMCP 应用
mcp = FastMCP("Library Document Search")

# 创建服务实例
library_server = LibraryMCPServer()


@mcp.tool()
async def search_documents(args: SearchDocumentsArgs) -> str:
    """
    搜索图书馆文献
    
    支持按关键词(K=)、作者(A=)、标题(T=)、机构(O=)等条件搜索。
    可以使用 AND、OR 等逻辑操作符组合多个条件。
    
    Args:
        args: 搜索参数，包含检索表达式、页面大小和页码
        
    Returns:
        格式化的搜索结果文本
        
    Examples:
        - search_documents({"query": "(K=Python)", "page_size": 5})
        - search_documents({"query": "(A=张三)", "page_size": 10, "page_index": 1})
        - search_documents({"query": "(K=机器学习) AND O=清华大学"})
    """
    try:
        # 确保有有效令牌
        library_server._ensure_valid_token()
        
        # 执行搜索
        search_request = DocumentSearchRequest(
            Rule=args.query,
            PageSize=args.page_size,
            PageIndex=args.page_index
        )
        
        result = library_server.document_service.search_documents(search_request)
        
        # 格式化结果
        documents_text = f"📚 检索到 {result.Total} 条文献记录（第 {result.PageIndex}/{result.TotalPages} 页）\n\n"
        
        for i, doc in enumerate(result.Documents, 1):
            documents_text += f"{i}. **{doc.Title}**\n"
            documents_text += f"   🆔 ID: {doc.Identifier}\n"
            if doc.Author:
                documents_text += f"   👤 作者: {doc.Author}\n"
            if doc.Publisher:
                documents_text += f"   🏢 出版社: {doc.Publisher}\n"
            if doc.PublishYear:
                documents_text += f"   📅 出版年份: {doc.PublishYear}\n"
            if doc.Subject:
                documents_text += f"   🏷️ 主题: {doc.Subject}\n"
            if doc.CallNumber:
                documents_text += f"   📖 索书号: {doc.CallNumber}\n"
            documents_text += "\n"
        
        if result.Total > len(result.Documents):
            documents_text += f"💡 提示: 还有更多结果，可以使用 page_index={result.PageIndex + 1} 查看下一页"
        
        return documents_text
        
    except Exception as e:
        return f"❌ 搜索失败: {str(e)}"


@mcp.tool()
async def get_document_detail(args: GetDocumentDetailArgs) -> str:
    """
    根据文献ID获取详细信息
    
    获取文献的完整信息，包括摘要、关键词、馆藏位置、借阅状态等。
    
    Args:
        args: 包含文献ID的参数
        
    Returns:
        格式化的文献详细信息文本
        
    Examples:
        - get_document_detail({"document_id": "DOC001"})
        - get_document_detail({"document_id": "DOC007"})
    """
    try:
        # 确保有有效令牌
        library_server._ensure_valid_token()
        
        # 获取详情
        detail_result = library_server.document_service.get_document_detail(args.document_id)
        doc = detail_result.Document
        
        # 格式化详情
        detail_text = f"📖 **文献详细信息**\n\n"
        detail_text += f"**📋 标题**: {doc.Title}\n"
        detail_text += f"**🆔 ID**: {doc.Identifier}\n"
        
        if doc.Author:
            detail_text += f"**👤 作者**: {doc.Author}\n"
        if doc.Publisher:
            detail_text += f"**🏢 出版社**: {doc.Publisher}\n"
        if doc.PublishYear:
            detail_text += f"**📅 出版年份**: {doc.PublishYear}\n"
        if doc.PublishPlace:
            detail_text += f"**🌍 出版地**: {doc.PublishPlace}\n"
        if doc.ISBN:
            detail_text += f"**📚 ISBN**: {doc.ISBN}\n"
        if doc.Pages:
            detail_text += f"**📄 页数**: {doc.Pages}\n"
        if doc.Language:
            detail_text += f"**🌐 语言**: {doc.Language}\n"
        if doc.Subject:
            detail_text += f"**🏷️ 主题**: {doc.Subject}\n"
        if doc.CallNumber:
            detail_text += f"**📖 索书号**: {doc.CallNumber}\n"
        
        if doc.Summary:
            detail_text += f"\n**📝 摘要**: {doc.Summary}\n"
        
        if doc.Keywords:
            detail_text += f"\n**🔑 关键词**: {', '.join(doc.Keywords)}\n"
        
        if doc.Location:
            detail_text += f"\n**📍 馆藏位置**: {doc.Location}\n"
        if doc.Status:
            detail_text += f"**📊 借阅状态**: {doc.Status}\n"
        
        return detail_text
        
    except Exception as e:
        return f"❌ 获取文献详情失败: {str(e)}"


@mcp.resource("library://auth/status")
async def get_auth_status() -> str:
    """获取认证状态"""
    import time
    current_time = time.time()
    
    if (library_server._access_token and 
        library_server._token_expires_at and 
        current_time < library_server._token_expires_at):
        
        remaining_time = library_server._token_expires_at - current_time
        return f"🔐 认证状态: 已认证\n⏰ 剩余时间: {int(remaining_time)} 秒\n🔄 自动刷新: 启用"
    else:
        return "🔐 认证状态: 未认证或已过期\n💡 下次调用工具时将自动获取新令牌"


@mcp.resource("library://info")
async def get_server_info() -> str:
    """获取服务器信息"""
    return """
🏛️ **图书馆文献查询 MCP 服务器**

📋 **服务信息**:
- 名称: Library Document Search
- 版本: 1.0.0 (FastMCP)
- 协议: MCP 2025-03-26

🛠️ **可用工具**:
1. **search_documents** - 搜索图书馆文献
   - 支持关键词、作者、标题、机构等条件搜索
   - 支持逻辑操作符 (AND, OR)
   - 分页支持

2. **get_document_detail** - 获取文献详细信息
   - 完整的文献信息
   - 包含摘要、关键词、馆藏信息

🔐 **认证**: 自动 OAuth2 令牌管理

📖 **使用示例**:
- 搜索: (K=Python)
- 作者: (A=张三)  
- 组合: (K=机器学习) AND O=清华大学
"""


if __name__ == "__main__":
    # 运行 FastMCP 服务器
    mcp.run()
