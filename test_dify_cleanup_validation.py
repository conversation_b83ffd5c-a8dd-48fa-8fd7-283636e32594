#!/usr/bin/env python3
"""
测试 Dify 清理时的 Pydantic 验证
模拟 Dify 在清理时对响应进行的验证
"""

import requests
import json
from pydantic import BaseModel, Field, ValidationError
from typing import Union, Optional, Any, Dict
from enum import Enum


class JSONRPCRequest(BaseModel):
    """JSON-RPC 请求模型"""
    jsonrpc: str = "2.0"
    method: str
    params: Optional[Dict[str, Any]] = None
    id: Union[str, int]


class JSONRPCNotification(BaseModel):
    """JSON-RPC 通知模型"""
    jsonrpc: str = "2.0"
    method: str
    params: Optional[Dict[str, Any]] = None


class JSONRPCResponse(BaseModel):
    """JSON-RPC 响应模型"""
    jsonrpc: str = "2.0"
    id: Union[str, int]  # Dify 要求 ID 必须是 string 或 int
    result: Optional[Dict[str, Any]] = None


class JSONRPCError(BaseModel):
    """JSON-RPC 错误模型"""
    jsonrpc: str = "2.0"
    id: Union[str, int]
    error: Dict[str, Any]


def test_pydantic_validation(response_data: dict) -> bool:
    """
    测试响应数据是否能通过 Pydantic 验证
    模拟 Dify 的验证逻辑
    """
    print(f"🔍 验证响应数据: {json.dumps(response_data, ensure_ascii=False)}")
    
    try:
        # 尝试验证为 JSONRPCResponse
        if 'result' in response_data:
            validated = JSONRPCResponse(**response_data)
            print(f"✅ JSONRPCResponse 验证通过: {validated.model_dump()}")
            return True
            
        # 尝试验证为 JSONRPCError  
        elif 'error' in response_data:
            validated = JSONRPCError(**response_data)
            print(f"✅ JSONRPCError 验证通过: {validated.model_dump()}")
            return True
            
        else:
            print("❌ 响应数据不包含 result 或 error 字段")
            return False
            
    except ValidationError as e:
        print(f"❌ Pydantic 验证失败:")
        for error in e.errors():
            print(f"  - {error['loc']}: {error['msg']} (input: {error['input']})")
        return False


def test_notification_response():
    """测试通知响应的 Pydantic 验证"""
    print("🧪 测试通知响应的 Pydantic 验证")
    print("=" * 50)
    
    # 发送无 ID 的通知请求
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📥 服务器响应: {json.dumps(response_data, ensure_ascii=False)}")
            
            # 测试 Pydantic 验证
            if test_pydantic_validation(response_data):
                print("✅ 通知响应通过 Pydantic 验证")
                return True
            else:
                print("❌ 通知响应未通过 Pydantic 验证")
                return False
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_with_id_notification():
    """测试带 ID 的通知响应"""
    print("\n🧪 测试带 ID 的通知响应")
    print("=" * 50)
    
    # 发送带 ID 的通知请求
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {},
        "id": 123
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📥 服务器响应: {json.dumps(response_data, ensure_ascii=False)}")
            
            # 测试 Pydantic 验证
            if test_pydantic_validation(response_data):
                print("✅ 带 ID 通知响应通过 Pydantic 验证")
                return True
            else:
                print("❌ 带 ID 通知响应未通过 Pydantic 验证")
                return False
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Dify 清理时的 Pydantic 验证测试")
    print("🎯 确保响应能通过 Dify 的 Pydantic 验证器")
    print("=" * 60)
    
    # 先启动服务器
    import subprocess
    import time
    
    print("🔄 启动测试服务器...")
    server_process = subprocess.Popen(
        ["python", "dify_mcp_server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    # 等待服务器启动
    time.sleep(3)
    
    try:
        # 执行测试
        tests = [
            ("无 ID 通知响应验证", test_notification_response),
            ("带 ID 通知响应验证", test_with_id_notification),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}")
            print("-" * 40)
            
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                results.append((test_name, False))
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("🏁 Pydantic 验证测试结果")
        print("=" * 60)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
        
        if passed == len(results):
            print("🎉 所有 Pydantic 验证测试通过！")
            print("💡 响应格式完全符合 Dify 的要求")
            print("🔧 应该能解决 Dify 清理时的验证错误")
        else:
            print("⚠️  部分验证测试失败，需要进一步调整")
            
    finally:
        # 关闭服务器
        print("\n🔄 关闭测试服务器...")
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    main()