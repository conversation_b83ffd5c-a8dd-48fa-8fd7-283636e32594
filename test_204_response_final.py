#!/usr/bin/env python3
"""
测试 204 No Content 响应是否能解决 Dify 的 SessionMessage 问题
"""

import requests
import json
import time


def test_204_notification():
    """测试 204 响应的通知"""
    print("🧪 测试 204 No Content 响应...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送请求: {json.dumps(notification_request, ensure_ascii=False)}")
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.content)} bytes")
        print(f"📄 Content-Type: {response.headers.get('content-type', 'None')}")
        print(f"📝 响应内容: '{response.text}'")
        
        if response.status_code == 204:
            if len(response.content) == 0:
                print("✅ 204 No Content - 完全无响应内容")
                print("💡 这应该能避免 Dify 的 JSON-RPC 解析")
                return True
            else:
                print("⚠️  204 但有响应内容")
                return True
        else:
            print(f"❌ 期望 204，实际 {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_complete_sequence_with_204():
    """测试带 204 响应的完整序列"""
    print("\n🎯 测试完整的 Dify 兼容序列...")
    
    # 1. 初始化
    print("1️⃣ 初始化请求...")
    init_request = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-03-26",
            "capabilities": {"sampling": {}, "roots": {"listChanged": True}},
            "clientInfo": {"name": "Dify", "version": "1.8.0"}
        },
        "id": 1
    }
    
    response = requests.post("http://localhost:8083/mcp", json=init_request, timeout=10)
    if response.status_code != 200:
        print(f"❌ 初始化失败: {response.status_code}")
        return False
    print("✅ 初始化成功")
    
    time.sleep(0.5)
    
    # 2. 通知
    print("2️⃣ 发送通知...")
    if not test_204_notification():
        return False
    
    time.sleep(0.5)
    
    # 3. 获取工具列表
    print("3️⃣ 获取工具列表...")
    tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 2
    }
    
    response = requests.post("http://localhost:8083/mcp", json=tools_request, timeout=10)
    if response.status_code != 200:
        print(f"❌ 工具列表失败: {response.status_code}")
        return False
    
    data = response.json()
    if not data.get('result', {}).get('tools'):
        print("❌ 工具列表为空")
        return False
    
    print("✅ 工具列表获取成功")
    return True


def main():
    """主测试函数"""
    print("🚀 测试 204 No Content 解决方案")
    print("🎯 这应该是最符合 JSON-RPC 规范的方式")
    print("=" * 60)
    
    # 启动服务器
    import subprocess
    
    print("🔄 启动 MCP 服务器...")
    server_process = subprocess.Popen(
        ["python", "dify_mcp_server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    time.sleep(3)
    
    try:
        # 执行测试
        print("📋 测试 204 No Content 响应")
        print("-" * 40)
        test_result = test_204_notification()
        
        if test_result:
            print("\n📋 测试完整序列")
            print("-" * 40)
            sequence_result = test_complete_sequence_with_204()
            
            print("\n" + "=" * 60)
            print("🏁 最终测试结果")
            print("=" * 60)
            
            if sequence_result:
                print("✅ 所有测试通过！")
                print("💡 204 No Content 响应应该能解决 SessionMessage 问题")
                print("🔧 解决方案: 对于无 ID 的通知返回 HTTP 204 No Content")
                print("📚 这完全符合 JSON-RPC 规范：通知不需要响应")
                print("\n🚀 在 Dify 中配置 MCP 服务:")
                print("   URL: http://localhost:8083/mcp")
                print("   显示名称: 图书馆文献查询")
                print("   服务器标识符: library-search")
            else:
                print("❌ 序列测试失败")
        else:
            print("❌ 204 响应测试失败")
            
    finally:
        print("\n🔄 关闭服务器...")
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    main()