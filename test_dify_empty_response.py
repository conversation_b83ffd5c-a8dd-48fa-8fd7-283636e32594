#!/usr/bin/env python3
"""
测试空响应是否能避免 SessionMessage 错误
"""

import requests
import json


def test_empty_response():
    """测试返回空对象的响应"""
    print("🧪 测试空对象响应")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 请求: {json.dumps(notification_request, indent=2)}")
        print(f"📥 状态码: {response.status_code}")
        print(f"📄 Content-Type: {response.headers.get('content-type')}")
        print(f"📝 响应内容: '{response.text}'")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"📋 解析后的响应: {json.dumps(response_data, indent=2)}")
                
                # 检查是否为空对象
                if response_data == {}:
                    print("✅ 返回空对象，可能避免 SessionMessage 错误")
                    return True
                else:
                    print("⚠️  返回非空对象")
                    return True
                    
            except json.JSONDecodeError:
                print("❌ 响应不是有效的 JSON")
                return False
        else:
            print(f"❌ 响应状态错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试空响应避免 SessionMessage 错误")
    print("=" * 50)
    
    # 启动服务器
    import subprocess
    import time
    
    print("🔄 启动服务器...")
    server_process = subprocess.Popen(
        ["python", "dify_mcp_server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    time.sleep(3)
    
    try:
        result = test_empty_response()
        
        print("\n" + "=" * 50)
        if result:
            print("✅ 测试通过 - 空响应可能解决 SessionMessage 问题")
            print("💡 建议在 Dify 中重新测试配置")
        else:
            print("❌ 测试失败")
            
    finally:
        print("\n🔄 关闭服务器...")
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    main()