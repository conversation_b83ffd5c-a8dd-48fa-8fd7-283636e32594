#!/usr/bin/env python3
"""
测试 204 No Content 响应
验证通知请求是否正确返回 204
"""

import requests
import json


def test_notification_204():
    """测试通知返回 204"""
    print("📢 测试通知返回 204 No Content...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送通知请求: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.content)} bytes")
        print(f"📄 Content-Type: {response.headers.get('content-type', 'None')}")
        print(f"📝 响应内容: '{response.text}'")
        
        if response.status_code == 204 and len(response.content) == 0:
            print("✅ 通知正确返回 204 No Content，无响应体")
            return True
        else:
            print("❌ 通知响应不正确")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_normal_requests_still_work():
    """测试正常请求仍然工作"""
    print("\n🔧 测试正常请求...")
    
    # 测试初始化
    init_request = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {"protocolVersion": "2025-03-26"},
        "id": 0
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=init_request,
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get('jsonrpc') == '2.0' and 'result' in response_data:
                print("✅ 初始化请求正常")
                return True
            else:
                print("❌ 初始化响应格式错误")
                return False
        else:
            print(f"❌ 初始化失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 初始化异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试 204 No Content 响应")
    print("=" * 40)
    
    # 测试通知 204 响应
    notification_ok = test_notification_204()
    
    # 测试正常请求
    normal_ok = test_normal_requests_still_work()
    
    print("\n" + "=" * 40)
    print("🏁 测试结果")
    print("=" * 40)
    
    if notification_ok and normal_ok:
        print("🎉 所有测试通过！")
        print("💡 通知返回 204，正常请求返回 200")
        print("🔧 这应该能解决 Dify 的 Pydantic 验证错误")
    else:
        print("⚠️  部分测试失败")
        if not notification_ok:
            print("   - 通知 204 响应问题")
        if not normal_ok:
            print("   - 正常请求问题")


if __name__ == "__main__":
    main()
