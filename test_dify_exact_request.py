#!/usr/bin/env python3
"""
测试 Dify 的确切请求格式
基于服务器日志中看到的实际请求
"""

import requests
import json


def test_dify_initialize_request():
    """测试 Dify 的初始化请求"""
    print("🔧 测试 Dify 的确切初始化请求...")
    
    # 这是从服务器日志中看到的 Dify 实际发送的请求
    dify_request = {
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-03-26",
            "capabilities": {
                "sampling": {},
                "roots": {
                    "listChanged": True
                }
            },
            "clientInfo": {
                "name": "Dify",
                "version": "1.8.0"
            }
        },
        "jsonrpc": "2.0",
        "id": 0  # 这是关键：Dify 使用整数 ID
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=dify_request,
            timeout=10
        )
        
        print(f"📤 发送请求: {json.dumps(dify_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ 请求成功！")
            print(f"📋 响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 请求失败")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_follow_up_requests():
    """测试后续请求"""
    print("\n🔄 测试后续请求...")
    
    # 测试通知
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    print("1. 发送初始化完成通知...")
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 通知成功")
        else:
            print(f"❌ 通知失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 通知异常: {e}")
    
    # 测试工具列表
    tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 1  # 使用整数 ID
    }
    
    print("2. 获取工具列表...")
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=tools_request,
            timeout=10
        )
        
        if response.status_code == 200:
            response_data = response.json()
            tools = response_data.get('result', {}).get('tools', [])
            print(f"✅ 获取到 {len(tools)} 个工具")
            for tool in tools:
                print(f"   - {tool.get('name')}")
        else:
            print(f"❌ 获取工具列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取工具列表异常: {e}")


def main():
    """主测试函数"""
    print("🚀 测试 Dify 的确切请求格式")
    print("=" * 50)
    
    # 测试初始化请求
    init_success = test_dify_initialize_request()
    
    if init_success:
        # 测试后续请求
        test_follow_up_requests()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成！")
        print("💡 如果所有测试都通过，那么 Dify 应该能够成功连接")
    else:
        print("\n" + "=" * 50)
        print("❌ 初始化测试失败")
        print("💡 需要进一步调试服务器问题")


if __name__ == "__main__":
    main()
