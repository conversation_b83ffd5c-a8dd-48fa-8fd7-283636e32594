# 图书馆文献查询 FastMCP 服务器

基于 FastMCP 框架实现的图书馆文献查询 MCP 服务器，提供标准的 MCP 协议支持，可以轻松集成到各种 AI 应用中。

## 🚀 特性

- ✅ **基于 FastMCP 框架** - 现代化的 Python MCP 实现
- ✅ **标准 MCP 协议** - 完全兼容 MCP 2025-03-26 规范
- ✅ **自动认证管理** - 内置 OAuth2 令牌自动刷新
- ✅ **丰富的工具集** - 文献搜索和详情查询
- ✅ **资源支持** - 提供认证状态和服务器信息
- ✅ **类型安全** - 使用 Pydantic 进行参数验证
- ✅ **异步支持** - 高性能异步处理

## 📦 安装依赖

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装 FastMCP
pip install fastmcp

# 验证安装
python -c "import fastmcp; print('FastMCP 安装成功')"
```

## 🛠️ 可用工具

### 1. search_documents
**功能**: 搜索图书馆文献

**参数**:
- `query` (string, 必需): 检索表达式
- `page_size` (integer, 可选): 每页数量，默认10，最大100
- `page_index` (integer, 可选): 页码，从1开始，默认1

**检索表达式格式**:
- `(K=关键词)` - 按关键词搜索
- `(A=作者名)` - 按作者搜索
- `(T=标题)` - 按标题搜索
- `(O=机构名)` - 按出版机构搜索
- `(K=关键词) AND O=机构` - 组合搜索

**示例**:
```json
{
  "query": "(K=Python)",
  "page_size": 5,
  "page_index": 1
}
```

### 2. get_document_detail
**功能**: 获取文献详细信息

**参数**:
- `document_id` (string, 必需): 文献的唯一标识符

**示例**:
```json
{
  "document_id": "DOC001"
}
```

## 📚 可用资源

### 1. library://auth/status
获取当前认证状态，包括令牌剩余时间和自动刷新状态。

### 2. library://info
获取服务器详细信息，包括版本、协议、可用工具等。

## 🚀 启动服务器

### 方法一：直接启动
```bash
python fastmcp_server.py
```

### 方法二：使用启动脚本
```bash
./start_fastmcp.sh
```

### 方法三：在其他目录启动
```bash
cd /path/to/your/project
python /Users/<USER>/src/HustLibraryDemo/fastmcp_server.py
```

## 🧪 测试服务器

运行测试脚本验证功能：

```bash
# 运行完整测试
python test_fastmcp_simple.py

# 预期输出：所有测试通过
```

## 🔧 MCP 客户端配置

### Claude Desktop 配置

在 `~/Library/Application Support/Claude/claude_desktop_config.json` 中添加：

```json
{
  "mcpServers": {
    "library-document-search": {
      "command": "python",
      "args": ["/Users/<USER>/src/HustLibraryDemo/fastmcp_server.py"],
      "cwd": "/Users/<USER>/src/HustLibraryDemo",
      "env": {
        "PYTHONPATH": "/Users/<USER>/src/HustLibraryDemo"
      }
    }
  }
}
```

### 其他 MCP 客户端

使用提供的配置文件 `fastmcp_config.json`，或根据客户端要求调整配置。

## 💡 使用示例

### 在 Claude 中使用

1. **搜索文献**:
   ```
   请帮我搜索Python编程相关的书籍，每页显示3本。
   ```

2. **获取详情**:
   ```
   请获取文献ID为DOC001的详细信息。
   ```

3. **组合查询**:
   ```
   搜索机器学习相关的文献，然后获取第一本书的详细信息。
   ```

### 在其他 AI 应用中使用

任何支持 MCP 协议的 AI 应用都可以连接到这个服务器，包括：
- Dify (如果支持 MCP)
- 自定义 MCP 客户端
- 其他 AI 开发平台

## 🔍 故障排除

### 常见问题

1. **ImportError: No module named 'fastmcp'**
   ```bash
   pip install fastmcp
   ```

2. **ImportError: No module named 'app'**
   ```bash
   # 确保在正确的目录运行
   cd /Users/<USER>/src/HustLibraryDemo
   python fastmcp_server.py
   ```

3. **认证失败**
   - 检查 `app/services/auth_service.py` 配置
   - 确保演示凭证正确

4. **端口占用或连接问题**
   - FastMCP 使用 STDIO 通信，不需要网络端口
   - 确保客户端配置正确

### 调试模式

启用详细日志：
```bash
# 设置环境变量
export FASTMCP_DEBUG=1
python fastmcp_server.py
```

## 📋 技术规格

- **框架**: FastMCP 2.12.0+
- **协议**: MCP (Model Context Protocol) 2025-03-26
- **传输**: STDIO (标准输入/输出)
- **数据格式**: JSON-RPC 2.0
- **认证**: 自动 OAuth2 令牌管理
- **Python**: 3.10+

## 🔄 与原版本对比

| 特性 | 原 HTTP 版本 | FastMCP 版本 |
|------|-------------|-------------|
| 协议支持 | 自定义 HTTP | 标准 MCP |
| 传输方式 | HTTP/JSON | STDIO/JSON-RPC |
| 客户端兼容性 | 有限 | 广泛支持 |
| 配置复杂度 | 高 | 低 |
| 错误处理 | 手动 | 自动 |
| 类型安全 | 部分 | 完整 |

## 🎯 下一步

1. **集成到 AI 应用**: 使用配置文件连接到支持 MCP 的 AI 应用
2. **扩展功能**: 添加更多工具和资源
3. **性能优化**: 根据使用情况优化查询性能
4. **监控日志**: 添加详细的使用日志和监控

---

**注意**: 这个 FastMCP 服务器完全兼容标准 MCP 协议，可以被任何支持 MCP 的客户端使用。
