#!/usr/bin/env python3
"""
测试空 JSON 响应
验证通知请求返回空字符串但有正确的 Content-Type
"""

import requests
import json


def test_notification_empty_json():
    """测试通知返回空 JSON 字符串"""
    print("📢 测试通知返回空 JSON 字符串...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送通知请求: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.content)} bytes")
        print(f"📄 Content-Type: {response.headers.get('content-type', 'None')}")
        print(f"📝 响应内容: '{response.text}'")
        
        if (response.status_code == 200 and 
            response.headers.get('content-type') == 'application/json' and
            response.text == '""'):  # 空字符串的 JSON 表示
            print("✅ 通知正确返回空 JSON 字符串，有正确的 Content-Type")
            return True
        else:
            print("❌ 通知响应不符合预期")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_notification_with_id():
    """测试带 ID 的通知"""
    print("\n📢 测试带 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {},
        "id": "test-notification"
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送带 ID 的通知: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📄 Content-Type: {response.headers.get('content-type', 'None')}")
        print(f"📝 响应内容: '{response.text}'")
        
        if (response.status_code == 200 and 
            response.headers.get('content-type') == 'application/json'):
            print("✅ 带 ID 的通知处理正常")
            return True
        else:
            print("❌ 带 ID 的通知处理异常")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_complete_flow():
    """测试完整的 MCP 流程"""
    print("\n🔄 测试完整的 MCP 流程...")
    
    # 1. 初始化
    print("1️⃣ 初始化...")
    init_request = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {"protocolVersion": "2025-03-26"},
        "id": 0
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            json=init_request,
            timeout=10
        )
        if response.status_code == 200:
            print("✅ 初始化成功")
        else:
            print(f"❌ 初始化失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 初始化异常: {e}")
        return False
    
    # 2. 通知
    print("2️⃣ 发送通知...")
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            json=notification_request,
            timeout=10
        )
        if response.status_code == 200:
            print("✅ 通知成功")
        else:
            print(f"❌ 通知失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 通知异常: {e}")
        return False
    
    # 3. 获取工具列表
    print("3️⃣ 获取工具列表...")
    tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 1
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            json=tools_request,
            timeout=10
        )
        if response.status_code == 200:
            tools_data = response.json()
            tools = tools_data.get('result', {}).get('tools', [])
            print(f"✅ 获取到 {len(tools)} 个工具")
        else:
            print(f"❌ 获取工具列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取工具列表异常: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 测试空 JSON 响应修复")
    print("🎯 确保通知有正确的 Content-Type")
    print("=" * 50)
    
    # 执行所有测试
    tests = [
        ("通知空 JSON 响应", test_notification_empty_json),
        ("带 ID 的通知", test_notification_with_id),
        ("完整 MCP 流程", test_complete_flow),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("🏁 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！")
        print("💡 通知返回空 JSON 字符串，有正确的 Content-Type")
        print("🔧 这应该能解决 Dify 的 Content-Type 错误")
        print("\n🚀 现在可以在 Dify 中配置 MCP 服务:")
        print("   URL: http://172.25.234.82:8083/mcp")
    else:
        print("⚠️  部分测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
