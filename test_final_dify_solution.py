#!/usr/bin/env python3
"""
最终的 Dify 兼容性测试
测试所有可能的 MCP 交互场景
"""

import requests
import json
import time


def test_initialize():
    """测试初始化请求"""
    print("🔧 测试初始化请求...")
    
    init_request = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-03-26",
            "capabilities": {
                "sampling": {},
                "roots": {"listChanged": True}
            },
            "clientInfo": {"name": "Dify", "version": "1.8.0"}
        },
        "id": 1
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            json=init_request,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('result') and data.get('id') == 1:
                print("✅ 初始化请求成功")
                return True
        
        print(f"❌ 初始化失败: {response.status_code}")
        return False
        
    except Exception as e:
        print(f"❌ 初始化异常: {e}")
        return False


def test_notification_no_id():
    """测试无 ID 的通知（这是导致 SessionMessage 错误的原因）"""
    print("📢 测试无 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送: {json.dumps(notification_request, ensure_ascii=False)}")
        print(f"📥 状态码: {response.status_code}")
        print(f"📝 响应: '{response.text}'")
        
        # 检查响应
        if response.status_code == 200:
            if response.text == '{}':
                print("✅ 通知返回空对象 - 应该避免 SessionMessage 错误")
                return True
            else:
                print("⚠️  通知返回非空对象")
                return True
        else:
            print(f"❌ 通知失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 通知异常: {e}")
        return False


def test_notification_with_id():
    """测试带 ID 的通知"""
    print("📢 测试带 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized", 
        "params": {},
        "id": 2
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            json=notification_request,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('id') == 2 and 'result' in data:
                print("✅ 带 ID 的通知成功")
                return True
        
        print(f"❌ 带 ID 通知失败: {response.status_code}")
        return False
        
    except Exception as e:
        print(f"❌ 带 ID 通知异常: {e}")
        return False


def test_tools_list():
    """测试工具列表"""
    print("🔨 测试工具列表...")
    
    tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": 3
    }
    
    try:
        response = requests.post(
            "http://localhost:8083/mcp",
            json=tools_request,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('result', {}).get('tools'):
                print("✅ 工具列表获取成功")
                return True
        
        print(f"❌ 工具列表失败: {response.status_code}")
        return False
        
    except Exception as e:
        print(f"❌ 工具列表异常: {e}")
        return False


def test_complete_dify_sequence():
    """测试完整的 Dify 交互序列"""
    print("🎯 测试完整的 Dify 交互序列...")
    
    # 1. 初始化
    if not test_initialize():
        return False
    
    time.sleep(0.5)
    
    # 2. 发送通知（这是问题所在）
    if not test_notification_no_id():
        return False
    
    time.sleep(0.5)
    
    # 3. 获取工具列表
    if not test_tools_list():
        return False
    
    print("✅ 完整序列测试通过")
    return True


def main():
    """主测试函数"""
    print("🚀 最终的 Dify MCP 兼容性测试")
    print("🎯 测试 SessionMessage 错误的解决方案")
    print("=" * 60)
    
    # 启动服务器
    import subprocess
    
    print("🔄 启动 MCP 服务器...")
    server_process = subprocess.Popen(
        ["python", "dify_mcp_server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    time.sleep(3)
    
    try:
        # 执行测试
        tests = [
            ("初始化请求", test_initialize),
            ("无 ID 通知", test_notification_no_id),
            ("带 ID 通知", test_notification_with_id),
            ("工具列表", test_tools_list),
            ("完整序列", test_complete_dify_sequence),
        ]
        
        results = []
        for test_name, test_func in tests[:-1]:  # 不重复执行完整序列
            print(f"\n📋 {test_name}")
            print("-" * 40)
            
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                results.append((test_name, False))
        
        # 显示结果
        print("\n" + "=" * 60)
        print("🏁 最终测试结果")
        print("=" * 60)
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
        
        if passed == len(results):
            print("🎉 所有测试通过！")
            print("💡 SessionMessage 错误应该已经解决")
            print("🔧 解决方案: 对于无 ID 的通知返回空对象 {}")
            print("\n🚀 现在可以在 Dify 中重新配置 MCP 服务:")
            print("   URL: http://localhost:8083/mcp")
            print("   显示名称: 图书馆文献查询")
            print("   服务器标识符: library-search")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
            
    finally:
        print("\n🔄 关闭服务器...")
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    main()