#!/usr/bin/env python3
"""
Dify 兼容的 MCP 服务器
基于 FastAPI 实现，符合 Dify MCP 工具提供商的要求
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, List, Optional
import asyncio
import uvicorn
import json

# 导入现有的服务
from fastmcp_server import library_server


class MCPRequest(BaseModel):
    """MCP 请求模型"""
    jsonrpc: str = Field(default="2.0")
    method: str
    params: Optional[Dict[str, Any]] = None
    id: Optional[str] = None


class MCPResponse(BaseModel):
    """MCP 响应模型"""
    jsonrpc: str = Field(default="2.0")
    id: Optional[Any] = None  # 允许字符串或整数
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

    class Config:
        # 排除 None 值，避免 Pydantic 验证问题
        exclude_none = True


# 创建 FastAPI 应用
app = FastAPI(
    title="Dify MCP 服务器",
    description="符合 Dify MCP 工具提供商要求的图书馆文献查询服务",
    version="1.0.0"
)

# 添加 CORS 支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """根路径 - MCP 服务器信息"""
    return {
        "name": "Library Document Search MCP Server",
        "version": "1.0.0",
        "description": "图书馆文献查询 MCP 服务器，兼容 Dify",
        "protocol": "MCP 2025-03-26",
        "transport": "HTTP",
        "capabilities": {
            "tools": True,
            "resources": True
        },
        "endpoints": {
            "mcp": "/mcp",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 测试认证
        token = library_server._ensure_valid_token()
        return {
            "status": "healthy",
            "timestamp": "2025-09-02T14:45:00Z",
            "auth": "valid" if token else "invalid",
            "tools": ["search_documents", "get_document_detail"]
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


@app.post("/mcp")
async def mcp_endpoint(request: Request):
    """
    MCP 协议端点
    处理所有 MCP 请求
    """
    try:
        # 直接解析 JSON 请求体，避免 Pydantic 验证问题
        body = await request.json()

        # 添加调试日志
        print(f"📥 收到 MCP 请求: {json.dumps(body, indent=2, ensure_ascii=False)}")

        method = body.get("method")
        params = body.get("params", {})
        request_id = body.get("id")
        
        if method == "initialize":
            response_data = {
                "jsonrpc": "2.0",
                "result": {
                    "protocolVersion": "2025-03-26",
                    "capabilities": {
                        "tools": {},
                        "resources": {}
                    },
                    "serverInfo": {
                        "name": "Library Document Search",
                        "version": "1.0.0"
                    }
                }
            }
            if request_id is not None:
                response_data["id"] = request_id

            return JSONResponse(
                content=response_data,
                status_code=200,
                headers={"Content-Type": "application/json"}
            )
        
        elif method == "notifications/initialized":
            # 处理初始化完成通知
            # Dify 期望所有响应都包含 id 字段，即使对于通知
            response_data = {
                "jsonrpc": "2.0",
                "id": request_id if request_id is not None else "notification",
                "result": {}
            }

            return JSONResponse(
                content=response_data,
                status_code=200,
                headers={"Content-Type": "application/json"}
            )
        
        elif method == "tools/list":
            response_data = {
                "jsonrpc": "2.0",
                "result": {
                    "tools": [
                        {
                            "name": "search_documents",
                            "description": "搜索图书馆文献，支持关键词、作者、标题等条件搜索",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "检索表达式，如：(K=Python)、(A=张三)"
                                    },
                                    "page_size": {
                                        "type": "integer",
                                        "description": "每页数量，默认10",
                                        "default": 10,
                                        "minimum": 1,
                                        "maximum": 100
                                    },
                                    "page_index": {
                                        "type": "integer",
                                        "description": "页码，从1开始",
                                        "default": 1,
                                        "minimum": 1
                                    }
                                },
                                "required": ["query"]
                            }
                        },
                        {
                            "name": "get_document_detail",
                            "description": "根据文献ID获取详细信息",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "document_id": {
                                        "type": "string",
                                        "description": "文献的唯一标识符，如：DOC001"
                                    }
                                },
                                "required": ["document_id"]
                            }
                        }
                    ]
                }
            }
            if request_id is not None:
                response_data["id"] = request_id

            return JSONResponse(
                content=response_data,
                status_code=200,
                headers={"Content-Type": "application/json"}
            )
        
        elif method == "tools/call":
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name == "search_documents":
                result = await handle_search_documents(arguments)
                response_data = {
                    "jsonrpc": "2.0",
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result
                            }
                        ]
                    }
                }
                if request_id is not None:
                    response_data["id"] = request_id

                return JSONResponse(
                    content=response_data,
                    status_code=200,
                    headers={"Content-Type": "application/json"}
                )

            elif tool_name == "get_document_detail":
                result = await handle_get_document_detail(arguments)
                response_data = {
                    "jsonrpc": "2.0",
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result
                            }
                        ]
                    }
                }
                if request_id is not None:
                    response_data["id"] = request_id

                return JSONResponse(
                    content=response_data,
                    status_code=200,
                    headers={"Content-Type": "application/json"}
                )
            
            else:
                response_data = {
                    "jsonrpc": "2.0",
                    "error": {
                        "code": -32601,
                        "message": f"Unknown tool: {tool_name}"
                    }
                }
                if request_id is not None:
                    response_data["id"] = request_id

                return JSONResponse(
                    content=response_data,
                    status_code=200,
                    headers={"Content-Type": "application/json"}
                )

        else:
            response_data = {
                "jsonrpc": "2.0",
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }
            if request_id is not None:
                response_data["id"] = request_id

            return JSONResponse(
                content=response_data,
                status_code=200,
                headers={"Content-Type": "application/json"}
            )
    
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析错误: {e}")
        return JSONResponse(
            status_code=400,
            content={
                "jsonrpc": "2.0",
                "id": None,
                "error": {
                    "code": -32700,
                    "message": "Parse error"
                }
            }
        )
    except Exception as e:
        print(f"❌ 内部错误: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "jsonrpc": "2.0",
                "id": body.get("id") if 'body' in locals() else None,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
        )


async def handle_search_documents(arguments: Dict[str, Any]) -> str:
    """处理搜索文献请求"""
    try:
        # 确保有有效令牌
        library_server._ensure_valid_token()
        
        # 获取参数
        query = arguments.get("query")
        page_size = arguments.get("page_size", 10)
        page_index = arguments.get("page_index", 1)
        
        if not query:
            return "❌ 错误：缺少必需参数 'query'"
        
        # 调用业务逻辑
        from app.models.document import DocumentSearchRequest
        
        search_request = DocumentSearchRequest(
            Rule=query,
            PageSize=page_size,
            PageIndex=page_index
        )
        
        result = library_server.document_service.search_documents(search_request)
        
        # 格式化结果
        documents_text = f"📚 检索到 {result.Total} 条文献记录（第 {result.PageIndex}/{result.TotalPages} 页）\n\n"
        
        for i, doc in enumerate(result.Documents, 1):
            documents_text += f"{i}. **{doc.Title}**\n"
            documents_text += f"   🆔 ID: {doc.Identifier}\n"
            if doc.Author:
                documents_text += f"   👤 作者: {doc.Author}\n"
            if doc.Publisher:
                documents_text += f"   🏢 出版社: {doc.Publisher}\n"
            if doc.PublishYear:
                documents_text += f"   📅 出版年份: {doc.PublishYear}\n"
            documents_text += "\n"
        
        if result.Total > len(result.Documents):
            documents_text += f"💡 提示: 还有更多结果，可以使用 page_index={result.PageIndex + 1} 查看下一页"
        
        return documents_text
        
    except Exception as e:
        return f"❌ 搜索失败: {str(e)}"


async def handle_get_document_detail(arguments: Dict[str, Any]) -> str:
    """处理获取文献详情请求"""
    try:
        # 确保有有效令牌
        library_server._ensure_valid_token()
        
        # 获取参数
        document_id = arguments.get("document_id")
        
        if not document_id:
            return "❌ 错误：缺少必需参数 'document_id'"
        
        # 调用业务逻辑
        detail_result = library_server.document_service.get_document_detail(document_id)
        doc = detail_result.Document
        
        # 格式化详情
        detail_text = f"📖 **文献详细信息**\n\n"
        detail_text += f"**📋 标题**: {doc.Title}\n"
        detail_text += f"**🆔 ID**: {doc.Identifier}\n"
        
        if doc.Author:
            detail_text += f"**👤 作者**: {doc.Author}\n"
        if doc.Publisher:
            detail_text += f"**🏢 出版社**: {doc.Publisher}\n"
        if doc.PublishYear:
            detail_text += f"**📅 出版年份**: {doc.PublishYear}\n"
        if doc.Summary:
            detail_text += f"\n**📝 摘要**: {doc.Summary}\n"
        if doc.Keywords:
            detail_text += f"\n**🔑 关键词**: {', '.join(doc.Keywords)}\n"
        
        return detail_text
        
    except Exception as e:
        return f"❌ 获取文献详情失败: {str(e)}"


if __name__ == "__main__":
    port = 8083
    local_ip = "*************"
    
    print(f"🚀 启动 Dify MCP 服务器...")
    print(f"📡 服务地址:")
    print(f"  - 本地访问: http://localhost:{port}")
    print(f"  - 局域网访问: http://{local_ip}:{port}")
    print(f"🔧 Dify MCP 配置:")
    print(f"  - MCP 端点: http://{local_ip}:{port}/mcp")
    print(f"  - 健康检查: http://{local_ip}:{port}/health")
    print(f"  - API 文档: http://{local_ip}:{port}/docs")
    print("✅ 服务启动中...")
    
    uvicorn.run(
        "dify_mcp_server:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
