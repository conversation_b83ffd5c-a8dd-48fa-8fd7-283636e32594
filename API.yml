openapi: 3.0.0
info:
  title: 图书馆文献查询全流程 API
  description: |
    该 API 套件提供了从用户身份认证到馆藏文献检索和详情查询的完整功能。
    **标准调用流程如下:**
    1.  首先，使用 `Authentication` 标签下的接口完成认证，获取 `access_token`。
    2.  然后，在调用 `文献查询` 标签下的接口时，将获取到的 `access_token` 放入 HTTP 请求头的 `Authorization` 字段中（格式: `Bearer <your_access_token>`）。
  version: "1.0.0"

servers:
  - url: http://*************:11333
    description: 主服务器

tags:
  - name: Authentication
    description: 授权与令牌管理接口，用于获取访问业务接口的凭证。
  - name: 文献查询 (Document Search)
    description: 馆藏文献的检索与详情查询接口。

# ----------------- 组件定义 (可复用部分) -----------------
components:
  # --- 安全方案定义 ---
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      description: |
        通过认证接口获取的 Access Token。
        在请求头中添加 'Authorization: Bearer {token}'。
      bearerFormat: JWT

  # --- 数据模型定义 ---
  schemas:
    TokenResponse:
      type: object
      description: 包含访问令牌和相关信息的响应体。
      properties:
        access_token:
          type: string
          description: 访问令牌。
        refresh_token:
          type: string
          description: 刷新令牌。
        token_type:
          type: string
          description: 令牌类型，通常为 Bearer。
          example: "Bearer"
        expires_in:
          type: integer
          description: 访问令牌的有效期（秒），例如 30。
          example: 30

# ----------------- 接口路径定义 -----------------
paths:
  # ======================================================
  # ============== Authentication Endpoints ==============
  # ======================================================
  /api/oauth/AccessTokenCode:
    post:
      tags:
        - Authentication
      summary: 第1步：获取授权码 (Code)
      description: 使用 AppID 和 AppSecret 换取一个临时的授权码 (code)，此 code 用于下一步换取 Token。
      operationId: getAccessTokenCode
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [appid, appsecret]
              properties:
                appid: { type: string, description: "应用ID" }
                appsecret: { type: string, description: "应用密钥" }
      responses:
        '200':
          description: 成功获取授权码。
          content:
            application/json:
              schema: { properties: { code: { type: string } } }

  /api/oauth/AccessToken:
    post:
      tags:
        - Authentication
      summary: 第2步：获取访问令牌 (Access Token)
      description: 使用上一步获取的授权码 (code) 换取可用于访问业务接口的 `access_token`。
      operationId: getAccessToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [code]
              properties:
                code: { type: string, description: "从上一步获取的授权码" }
      responses:
        '200':
          description: 成功获取访问令牌。
          content:
            application/json:
              schema: { $ref: '#/components/schemas/TokenResponse' }

  /api/oauth/refreshToken:
    post:
      tags:
        - Authentication
      summary: 第3步：刷新访问令牌
      description: 当 `access_token` 过期后，使用 `refresh_token` 获取一个新的 `access_token`。
      operationId: refreshAccessToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [refreshToken]
              properties:
                refreshToken: { type: string, description: "获取Token时一并返回的刷新令牌" }
      responses:
        '200':
          description: 成功刷新访问令牌。
          content:
            application/json:
              schema: { $ref: '#/components/schemas/TokenResponse' }
              
  # ======================================================
  # ============ Document Search Endpoints ===============
  # ======================================================
  /t/p/articlesearch/PLCarticlesearch:
    post:
      tags:
        - 文献查询 (Document Search)
      summary: 检索纸本馆藏文献列表
      description: 根据关键词、作者等信息，分页检索文献。**调用此接口前，请先完成认证流程获取`access_token`**。
      operationId: searchPaperCollectionDocuments
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [Rule]
              properties:
                Rule:
                  type: string
                  description: "检索表达式。例如: (K=图书馆学) AND O=武汉大学"
                PageSize:
                  type: integer
                  default: 10
                PageIndex:
                  type: integer
                  default: 1
      responses:
        '200':
          description: 检索成功，返回文献列表。
          content:
            application/json:
              schema: { type: object } # Schema can be further detailed here

  /t/p1/articlesearch/PLCarticledetail:
    get:
      tags:
        - 文献查询 (Document Search)
      summary: 获取指定ID的文献详细信息
      description: 根据文献的唯一ID获取其详细信息（如索书号）。`Identifier` 来自文献检索列表的返回结果。**调用此接口前，请先完成认证流程获取`access_token`**。
      operationId: getPaperCollectionDocumentDetail
      security:
        - bearerAuth: []
      parameters:
        - name: Identifier
          in: query
          required: true
          description: 文献的唯一ID。
          schema:
            type: string
      responses:
        '200':
          description: 获取详情成功。
          content:
            application/json:
              schema: { type: object } # Schema can be further detailed here

