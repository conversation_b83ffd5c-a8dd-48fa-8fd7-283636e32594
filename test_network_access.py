#!/usr/bin/env python3
"""
测试 MCP 桥接服务的网络访问
验证本地和局域网访问是否正常
"""

import requests
import socket
import subprocess
import platform
import time
from concurrent.futures import ThreadPoolExecutor, as_completed


def get_network_info():
    """获取网络信息"""
    print("🌐 获取网络信息...")
    
    try:
        # 获取本机所有IP地址
        hostname = socket.gethostname()
        print(f"   主机名: {hostname}")
        
        # 获取所有网络接口
        if platform.system() in ['Darwin', 'Linux']:
            try:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                
                current_interface = None
                for line in lines:
                    if line and not line.startswith(' ') and not line.startswith('\t'):
                        current_interface = line.split(':')[0]
                    elif 'inet ' in line and '127.0.0.1' not in line:
                        parts = line.strip().split()
                        for i, part in enumerate(parts):
                            if part == 'inet' and i + 1 < len(parts):
                                ip = parts[i + 1]
                                print(f"   {current_interface}: {ip}")
            except Exception as e:
                print(f"   无法获取网络接口信息: {e}")
        
        # 检查目标IP是否可达
        target_ip = "*************"
        try:
            # 尝试ping目标IP
            if platform.system() == 'Darwin':
                result = subprocess.run(['ping', '-c', '1', '-W', '1000', target_ip], 
                                      capture_output=True, text=True, timeout=5)
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', target_ip], 
                                      capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print(f"✅ 目标IP {target_ip} 可达")
            else:
                print(f"❌ 目标IP {target_ip} 不可达")
        except Exception as e:
            print(f"⚠️  无法测试IP可达性: {e}")
            
    except Exception as e:
        print(f"❌ 获取网络信息失败: {e}")


def test_service_endpoint(base_url, name):
    """测试单个服务端点"""
    try:
        # 测试根路径
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return {
                "name": name,
                "url": base_url,
                "status": "✅ 可访问",
                "service": data.get("service", "未知"),
                "response_time": response.elapsed.total_seconds()
            }
        else:
            return {
                "name": name,
                "url": base_url,
                "status": f"❌ HTTP {response.status_code}",
                "response_time": response.elapsed.total_seconds()
            }
    except requests.exceptions.ConnectTimeout:
        return {
            "name": name,
            "url": base_url,
            "status": "❌ 连接超时"
        }
    except requests.exceptions.ConnectionError:
        return {
            "name": name,
            "url": base_url,
            "status": "❌ 连接失败"
        }
    except Exception as e:
        return {
            "name": name,
            "url": base_url,
            "status": f"❌ 错误: {str(e)}"
        }


def test_all_endpoints():
    """测试所有端点"""
    print("\n🧪 测试服务端点...")
    
    endpoints = [
        ("本地访问", "http://localhost:8082"),
        ("127.0.0.1", "http://127.0.0.1:8082"),
        ("局域网IP", "http://*************:8082"),
    ]
    
    # 并发测试所有端点
    with ThreadPoolExecutor(max_workers=3) as executor:
        future_to_endpoint = {
            executor.submit(test_service_endpoint, url, name): (name, url)
            for name, url in endpoints
        }
        
        results = []
        for future in as_completed(future_to_endpoint):
            result = future.result()
            results.append(result)
    
    # 显示结果
    print("\n📊 测试结果:")
    print("-" * 60)
    for result in sorted(results, key=lambda x: x["name"]):
        print(f"{result['status']} {result['name']}")
        print(f"   URL: {result['url']}")
        if "response_time" in result:
            print(f"   响应时间: {result['response_time']:.3f}s")
        if "service" in result:
            print(f"   服务: {result['service']}")
        print()
    
    return results


def test_api_functionality():
    """测试API功能"""
    print("🔧 测试API功能...")
    
    # 使用可访问的端点进行功能测试
    test_urls = [
        "http://localhost:8082",
        "http://*************:8082"
    ]
    
    for base_url in test_urls:
        print(f"\n📤 测试 {base_url}")
        
        try:
            # 测试健康检查
            health_response = requests.get(f"{base_url}/health", timeout=5)
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"✅ 健康检查: {health_data.get('status')}")
                
                # 测试搜索API
                search_data = {"query": "(K=Python)", "page_size": 2}
                search_response = requests.post(
                    f"{base_url}/search",
                    json=search_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                if search_response.status_code == 200:
                    search_result = search_response.json()
                    if search_result.get("success"):
                        print("✅ 搜索API正常")
                    else:
                        print(f"❌ 搜索API失败: {search_result.get('error')}")
                else:
                    print(f"❌ 搜索API HTTP错误: {search_response.status_code}")
                
                # 成功测试一个端点就够了
                break
            else:
                print(f"❌ 健康检查失败: {health_response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            continue
    
    print("✅ API功能测试完成")


def check_firewall_and_network():
    """检查防火墙和网络配置"""
    print("\n🔒 检查网络配置...")
    
    # 检查端口是否被占用
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('*************', 8082))
        sock.close()
        
        if result == 0:
            print("✅ 端口8082在目标IP上可访问")
        else:
            print("❌ 端口8082在目标IP上不可访问")
    except Exception as e:
        print(f"⚠️  无法检查端口状态: {e}")
    
    # 检查本地端口监听
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8082))
        sock.close()
        
        if result == 0:
            print("✅ 本地端口8082正在监听")
        else:
            print("❌ 本地端口8082未监听")
    except Exception as e:
        print(f"⚠️  无法检查本地端口: {e}")
    
    # 提供防火墙配置建议
    print("\n💡 网络配置建议:")
    if platform.system() == 'Darwin':
        print("   macOS防火墙配置:")
        print("   1. 系统偏好设置 → 安全性与隐私 → 防火墙")
        print("   2. 允许Python或uvicorn通过防火墙")
        print("   3. 或者临时关闭防火墙进行测试")
    elif platform.system() == 'Linux':
        print("   Linux防火墙配置:")
        print("   sudo ufw allow 8082")
        print("   或者: sudo iptables -A INPUT -p tcp --dport 8082 -j ACCEPT")


def main():
    """主测试函数"""
    print("🚀 开始网络访问测试")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: MCP桥接服务局域网访问 (*************:8082)")
    print("=" * 70)
    
    # 获取网络信息
    get_network_info()
    
    # 检查网络配置
    check_firewall_and_network()
    
    # 测试所有端点
    results = test_all_endpoints()
    
    # 测试API功能
    test_api_functionality()
    
    # 总结
    print("\n" + "=" * 70)
    print("🏁 测试总结")
    print("=" * 70)
    
    accessible_endpoints = [r for r in results if "✅" in r["status"]]
    
    if accessible_endpoints:
        print(f"✅ 可访问的端点: {len(accessible_endpoints)}/{len(results)}")
        for endpoint in accessible_endpoints:
            print(f"   - {endpoint['name']}: {endpoint['url']}")
        
        print(f"\n🔧 Dify配置建议:")
        if any("局域网IP" in r["name"] for r in accessible_endpoints):
            print("   使用局域网IP配置Dify工具:")
            print("   - 搜索工具: http://*************:8082/search")
            print("   - 详情工具: http://*************:8082/detail")
        else:
            print("   局域网访问不可用，使用本地访问:")
            print("   - 搜索工具: http://localhost:8082/search")
            print("   - 详情工具: http://localhost:8082/detail")
    else:
        print("❌ 所有端点都不可访问")
        print("💡 请检查:")
        print("   1. MCP桥接服务是否正在运行")
        print("   2. 防火墙设置")
        print("   3. 网络连接")


if __name__ == "__main__":
    main()
