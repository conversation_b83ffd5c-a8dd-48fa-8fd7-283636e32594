#!/usr/bin/env python3
"""
测试 Dify MCP 桥接服务
验证 HTTP API 是否正常工作
"""

import requests
import json
import time


def test_health_check():
    """测试健康检查"""
    print("🏥 测试健康检查...")
    
    try:
        response = requests.get("http://localhost:8082/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务健康: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_search_api():
    """测试搜索 API"""
    print("\n🔍 测试搜索 API...")
    
    test_cases = [
        {
            "name": "搜索Python文献",
            "data": {"query": "(K=Python)", "page_size": 3}
        },
        {
            "name": "搜索作者张三",
            "data": {"query": "(A=张三)", "page_size": 5}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📤 {test_case['name']}")
        print(f"   请求: {test_case['data']}")
        
        try:
            response = requests.post(
                "http://localhost:8082/search",
                headers={"Content-Type": "application/json"},
                json=test_case['data'],
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 搜索成功")
                    # 显示结果的前100个字符
                    result_preview = data.get('data', '')[:100] + "..." if len(data.get('data', '')) > 100 else data.get('data', '')
                    print(f"   结果预览: {result_preview}")
                else:
                    print(f"❌ 搜索失败: {data.get('error')}")
            else:
                print(f"❌ HTTP 错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")


def test_detail_api():
    """测试详情 API"""
    print("\n📖 测试详情 API...")
    
    test_cases = [
        {"name": "获取DOC001详情", "document_id": "DOC001"},
        {"name": "获取DOC007详情", "document_id": "DOC007"}
    ]
    
    for test_case in test_cases:
        print(f"\n📤 {test_case['name']}")
        print(f"   文献ID: {test_case['document_id']}")
        
        try:
            response = requests.post(
                "http://localhost:8082/detail",
                headers={"Content-Type": "application/json"},
                json={"document_id": test_case['document_id']},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 获取详情成功")
                    # 显示结果的前100个字符
                    result_preview = data.get('data', '')[:100] + "..." if len(data.get('data', '')) > 100 else data.get('data', '')
                    print(f"   结果预览: {result_preview}")
                else:
                    print(f"❌ 获取详情失败: {data.get('error')}")
            else:
                print(f"❌ HTTP 错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")


def test_get_endpoints():
    """测试 GET 端点"""
    print("\n🌐 测试 GET 端点...")
    
    # 测试简化搜索接口
    print("\n📤 测试 GET 搜索接口")
    try:
        response = requests.get(
            "http://localhost:8082/search/(K=Python)?page_size=2",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ GET 搜索成功")
            else:
                print(f"❌ GET 搜索失败: {data.get('error')}")
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试简化详情接口
    print("\n📤 测试 GET 详情接口")
    try:
        response = requests.get("http://localhost:8082/detail/DOC001", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ GET 详情成功")
            else:
                print(f"❌ GET 详情失败: {data.get('error')}")
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_auth_status():
    """测试认证状态"""
    print("\n🔐 测试认证状态...")
    
    try:
        response = requests.get("http://localhost:8082/auth/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 认证状态: {data}")
        else:
            print(f"❌ 获取认证状态失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_service_info():
    """测试服务信息"""
    print("\n📋 测试服务信息...")
    
    try:
        response = requests.get("http://localhost:8082/", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务信息:")
            print(f"   名称: {data.get('service')}")
            print(f"   版本: {data.get('version')}")
            print(f"   描述: {data.get('description')}")
            
            # 显示 Dify 工具配置
            dify_tools = data.get('dify_tools', [])
            print(f"   Dify 工具数量: {len(dify_tools)}")
            for tool in dify_tools:
                print(f"     - {tool['name']}: {tool['method']} {tool['url']}")
        else:
            print(f"❌ 获取服务信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def main():
    """主测试函数"""
    print("🚀 开始测试 Dify MCP 桥接服务")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("服务信息", test_service_info),
        ("健康检查", test_health_check),
        ("认证状态", test_auth_status),
        ("搜索 API", test_search_api),
        ("详情 API", test_detail_api),
        ("GET 端点", test_get_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            test_func()
            results.append((test_name, True))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("🏁 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！桥接服务准备就绪")
        print("\n💡 下一步:")
        print("1. 在 Dify 中配置自定义工具")
        print("2. 使用提供的 URL 和参数配置")
        print("3. 参考 DIFY_INTEGRATION_GUIDE.md 进行详细配置")
    else:
        print("⚠️  部分测试失败，请检查服务状态")
        print("💡 确保桥接服务正在运行: python dify_mcp_bridge.py")


if __name__ == "__main__":
    main()
