# 图书馆文献查询API

基于 FastAPI 实现的图书馆文献查询系统，提供完整的OAuth2认证和文献检索功能。

## 项目特性

- ✅ 完整的OAuth2认证流程
- ✅ JWT令牌管理（访问令牌和刷新令牌）
- ✅ 文献检索和详情查询
- ✅ 支持复杂检索表达式
- ✅ RESTful API设计
- ✅ 完整的API文档（Swagger UI）
- ✅ 基于Python 3.10 + FastAPI
- ✅ venv虚拟环境管理

## 技术栈

- **框架**: FastAPI 0.104.1
- **Python**: 3.10+
- **认证**: JWT (python-jose)
- **ASGI服务器**: Uvicorn
- **数据验证**: Pydantic
- **虚拟环境**: venv

## 快速开始

### 1. 环境要求

确保已安装 Python 3.10+：
```bash
python3.10 --version
```

### 2. 激活虚拟环境

```bash
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 启动服务

```bash
# 方式1：使用启动脚本
python run.py

# 方式2：直接运行主模块
python -m app.main

# 方式3：使用uvicorn命令
uvicorn app.main:app --host 0.0.0.0 --port 11333 --reload
```

### 5. 访问API文档

启动成功后，访问以下地址：

- **API文档**: http://localhost:11333/docs
- **ReDoc文档**: http://localhost:11333/redoc
- **根路径**: http://localhost:11333/

## API使用说明

### 认证流程

1. **获取授权码**
   ```bash
   curl -X POST "http://localhost:11333/api/oauth/AccessTokenCode" \
     -H "Content-Type: application/json" \
     -d '{
       "appid": "demo_app_123",
       "appsecret": "demo_secret_456"
     }'
   ```

2. **获取访问令牌**
   ```bash
   curl -X POST "http://localhost:11333/api/oauth/AccessToken" \
     -H "Content-Type: application/json" \
     -d '{
       "code": "您在第1步获取的code"
     }'
   ```

3. **使用访问令牌查询文献**
   ```bash
   curl -X POST "http://localhost:11333/t/p/articlesearch/PLCarticlesearch" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer 您的access_token" \
     -d '{
       "Rule": "(K=图书馆学)",
       "PageSize": 10,
       "PageIndex": 1
     }'
   ```

### 检索表达式示例

- `(K=图书馆学)` - 搜索包含"图书馆学"关键词的文献
- `(K=图书馆学) AND O=武汉大学` - 搜索图书馆学相关且来自武汉大学的文献
- `(A=张三)` - 搜索作者为"张三"的文献
- `(T=信息检索)` - 搜索标题包含"信息检索"的文献

### 演示凭证

- **AppID**: `demo_app_123`
- **AppSecret**: `demo_secret_456`

## 项目结构

```
HustLibraryDemo/
├── venv/                   # Python虚拟环境
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 配置管理
│   ├── models/            # Pydantic模型
│   │   ├── __init__.py
│   │   ├── auth.py        # 认证相关模型
│   │   └── document.py    # 文献相关模型
│   ├── routers/           # API路由
│   │   ├── __init__.py
│   │   ├── auth.py        # 认证路由
│   │   └── document.py    # 文献查询路由
│   ├── services/          # 业务逻辑服务
│   │   ├── __init__.py
│   │   ├── auth_service.py       # 认证服务
│   │   └── document_service.py   # 文献查询服务
│   └── middleware/        # 中间件
│       ├── __init__.py
│       └── auth_middleware.py    # 认证中间件
├── documents/             # 接口文档
│   └── 接口.yml           # 原始接口规范
├── requirements.txt       # 项目依赖
├── .env                  # 环境变量
├── README.md             # 项目说明
└── run.py               # 启动脚本
```

## 配置说明

### 环境变量 (.env)

```bash
# 应用配置
APP_NAME=图书馆文献查询API
APP_VERSION=1.0.0
DEBUG=True

# 服务器配置
HOST=0.0.0.0
PORT=11333

# JWT配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用凭证 (模拟)
DEMO_APP_ID=demo_app_123
DEMO_APP_SECRET=demo_secret_456
```

## 开发说明

### 添加新的文献数据

编辑 `app/services/document_service.py` 文件中的 `_documents` 列表，添加新的文献记录。

### 修改认证逻辑

编辑 `app/services/auth_service.py` 文件，调整认证流程和令牌管理逻辑。

### 扩展API接口

在 `app/routers/` 目录下创建新的路由文件，然后在 `app/main.py` 中注册。

## 生产部署

### 1. 修改配置

- 将 `DEBUG` 设置为 `False`
- 修改 `SECRET_KEY` 为安全的随机字符串
- 配置合适的数据库连接（替换内存存储）
- 设置合适的CORS策略

### 2. 使用生产级ASGI服务器

```bash
# 使用Gunicorn + Uvicorn
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:11333

# 或者使用uvicorn（单进程）
uvicorn app.main:app --host 0.0.0.0 --port 11333 --workers 4
```

### 3. 反向代理

建议使用Nginx作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:11333;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 故障排除

### 常见问题

1. **端口已被占用**
   ```bash
   # 查看端口使用情况
   lsof -i :11333
   # 修改.env中的PORT配置
   ```

2. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   # 重新安装依赖
   pip install -r requirements.txt
   ```

3. **Token验证失败**
   - 检查Authorization头格式：`Bearer <token>`
   - 确认token未过期（默认30分钟）
   - 验证SECRET_KEY配置

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！