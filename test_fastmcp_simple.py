#!/usr/bin/env python3
"""
简化的 FastMCP 服务器测试脚本
直接测试业务逻辑功能
"""

import asyncio
from fastmcp_server import library_server, SearchDocumentsArgs, GetDocumentDetailArgs


async def test_authentication():
    """测试认证功能"""
    print("🔐 测试认证功能...")
    
    try:
        # 测试令牌获取
        token = library_server._ensure_valid_token()
        print(f"✅ 成功获取访问令牌: {token[:20]}...")
        
        # 测试令牌有效性
        import time
        if library_server._token_expires_at:
            remaining = library_server._token_expires_at - time.time()
            print(f"⏰ 令牌剩余时间: {int(remaining)} 秒")
        
        return True
    except Exception as e:
        print(f"❌ 认证测试失败: {e}")
        return False


async def test_search_function():
    """测试搜索功能的业务逻辑"""
    print("\n🔍 测试搜索功能...")
    
    try:
        # 直接测试业务逻辑
        from app.models.document import DocumentSearchRequest
        
        search_request = DocumentSearchRequest(
            Rule="(K=Python)",
            PageSize=3,
            PageIndex=1
        )
        
        result = library_server.document_service.search_documents(search_request)
        
        print(f"✅ 搜索成功:")
        print(f"   📊 总数: {result.Total}")
        print(f"   📄 当前页: {result.PageIndex}/{result.TotalPages}")
        print(f"   📚 返回文献数: {len(result.Documents)}")
        
        if result.Documents:
            print(f"   📖 第一条文献: {result.Documents[0].Title}")
        
        return True
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        return False


async def test_detail_function():
    """测试获取详情功能的业务逻辑"""
    print("\n📖 测试获取详情功能...")
    
    try:
        # 直接测试业务逻辑
        detail_result = library_server.document_service.get_document_detail("DOC001")
        doc = detail_result.Document
        
        print(f"✅ 获取详情成功:")
        print(f"   📋 标题: {doc.Title}")
        print(f"   🆔 ID: {doc.Identifier}")
        print(f"   👤 作者: {doc.Author}")
        print(f"   🏢 出版社: {doc.Publisher}")
        
        return True
    except Exception as e:
        print(f"❌ 详情测试失败: {e}")
        return False


def test_fastmcp_structure():
    """测试 FastMCP 结构"""
    print("\n📋 测试 FastMCP 结构...")
    
    try:
        from fastmcp_server import mcp
        
        print(f"✅ FastMCP 服务器信息:")
        print(f"   📛 名称: {mcp.name}")
        print(f"   🔧 类型: {type(mcp).__name__}")
        
        # 尝试获取工具信息
        if hasattr(mcp, 'tools'):
            print(f"   🛠️  工具数量: {len(mcp.tools)}")
        elif hasattr(mcp, '_tools'):
            print(f"   🛠️  工具数量: {len(mcp._tools)}")
        else:
            print("   🛠️  无法获取工具信息")
        
        return True
    except Exception as e:
        print(f"❌ FastMCP 结构测试失败: {e}")
        return False


async def test_mcp_tools_integration():
    """测试 MCP 工具集成"""
    print("\n🔧 测试 MCP 工具集成...")
    
    try:
        # 测试参数模型
        search_args = SearchDocumentsArgs(
            query="(K=Python)",
            page_size=5,
            page_index=1
        )
        print(f"✅ 搜索参数模型创建成功: {search_args.query}")
        
        detail_args = GetDocumentDetailArgs(document_id="DOC001")
        print(f"✅ 详情参数模型创建成功: {detail_args.document_id}")
        
        return True
    except Exception as e:
        print(f"❌ MCP 工具集成测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始 FastMCP 服务器简化测试")
    print("=" * 50)
    
    # 执行各项测试
    tests = [
        ("FastMCP 结构", test_fastmcp_structure),
        ("认证功能", test_authentication),
        ("搜索功能", test_search_function),
        ("详情功能", test_detail_function),
        ("MCP 工具集成", test_mcp_tools_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
        
        results.append((test_name, result))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("🏁 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！FastMCP 服务器准备就绪")
        print("\n💡 启动服务器:")
        print("   python fastmcp_server.py")
        print("   或者: ./start_fastmcp.sh")
    else:
        print("⚠️  部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
