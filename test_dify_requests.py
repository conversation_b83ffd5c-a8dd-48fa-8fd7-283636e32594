#!/usr/bin/env python3
"""
测试各种可能的 Dify MCP 请求格式
帮助调试 422 错误
"""

import requests
import json
import time


def test_various_request_formats():
    """测试各种请求格式"""
    base_url = "http://172.25.234.82:8083/mcp"
    
    # 测试不同的请求格式
    test_cases = [
        {
            "name": "标准 JSON-RPC 请求",
            "data": {
                "jsonrpc": "2.0",
                "method": "initialize",
                "params": {
                    "protocolVersion": "2025-03-26",
                    "capabilities": {}
                },
                "id": "test-1"
            }
        },
        {
            "name": "无 params 的请求",
            "data": {
                "jsonrpc": "2.0",
                "method": "tools/list",
                "id": "test-2"
            }
        },
        {
            "name": "通知类型请求（无 id）",
            "data": {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": {}
            }
        },
        {
            "name": "空 params 对象",
            "data": {
                "jsonrpc": "2.0",
                "method": "tools/list",
                "params": {},
                "id": "test-3"
            }
        },
        {
            "name": "null params",
            "data": {
                "jsonrpc": "2.0",
                "method": "tools/list",
                "params": None,
                "id": "test-4"
            }
        },
        {
            "name": "Dify 可能的格式 1",
            "data": {
                "method": "initialize",
                "params": {
                    "protocolVersion": "2025-03-26"
                },
                "id": "dify-1"
            }
        },
        {
            "name": "Dify 可能的格式 2",
            "data": {
                "jsonrpc": "2.0",
                "method": "initialize",
                "id": "dify-2"
            }
        }
    ]
    
    print("🧪 测试各种 MCP 请求格式...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   请求数据: {json.dumps(test_case['data'], ensure_ascii=False)}")
        
        try:
            response = requests.post(
                base_url,
                headers={"Content-Type": "application/json"},
                json=test_case['data'],
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"   ✅ 成功: {response_data.get('result', {}).get('serverInfo', {}).get('name', '无名称')}")
                except:
                    print(f"   ✅ 成功: 响应内容长度 {len(response.text)}")
            elif response.status_code == 422:
                print(f"   ❌ 422 错误: {response.text[:100]}...")
            else:
                print(f"   ⚠️  其他错误: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")


def test_raw_requests():
    """测试原始请求"""
    base_url = "http://172.25.234.82:8083/mcp"
    
    print("\n🔧 测试原始请求...")
    print("=" * 40)
    
    # 测试完全空的请求
    print("\n1. 空 JSON 对象")
    try:
        response = requests.post(
            base_url,
            headers={"Content-Type": "application/json"},
            json={},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"   响应: {response.text[:100]}...")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 测试无效 JSON
    print("\n2. 无效 JSON")
    try:
        response = requests.post(
            base_url,
            headers={"Content-Type": "application/json"},
            data="invalid json",
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"   响应: {response.text[:100]}...")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 测试缺少 Content-Type
    print("\n3. 缺少 Content-Type")
    try:
        response = requests.post(
            base_url,
            json={"jsonrpc": "2.0", "method": "tools/list", "id": "test"},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"   响应: {response.text[:100]}...")
    except Exception as e:
        print(f"   异常: {e}")


def test_health_and_root():
    """测试健康检查和根路径"""
    print("\n🏥 测试基础端点...")
    print("=" * 30)
    
    endpoints = [
        ("根路径", "http://172.25.234.82:8083/"),
        ("健康检查", "http://172.25.234.82:8083/health"),
        ("API 文档", "http://172.25.234.82:8083/docs")
    ]
    
    for name, url in endpoints:
        print(f"\n📤 {name}: {url}")
        try:
            response = requests.get(url, timeout=10)
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ 可访问")
            else:
                print(f"   ❌ 错误: {response.text[:50]}...")
        except Exception as e:
            print(f"   ❌ 异常: {e}")


def main():
    """主测试函数"""
    print("🚀 开始 Dify MCP 请求格式测试")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: http://172.25.234.82:8083/mcp")
    print()
    
    # 测试基础端点
    test_health_and_root()
    
    # 测试各种请求格式
    test_various_request_formats()
    
    # 测试原始请求
    test_raw_requests()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
    print("💡 如果看到 422 错误，请检查服务器日志以了解具体的请求格式")
    print("📋 服务器日志应该显示收到的请求内容")


if __name__ == "__main__":
    main()
