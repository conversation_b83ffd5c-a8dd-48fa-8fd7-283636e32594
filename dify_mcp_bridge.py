#!/usr/bin/env python3
"""
Dify MCP 桥接服务器
将 FastMCP 服务器包装成 HTTP API，供 Dify 调用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional
import asyncio
import uvicorn

# 导入 FastMCP 服务器组件
from fastmcp_server import library_server, SearchDocumentsArgs, GetDocumentDetailArgs


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(description="检索表达式，如：(K=Python)、(A=张三)")
    page_size: int = Field(default=10, ge=1, le=100, description="每页数量")
    page_index: int = Field(default=1, ge=1, description="页码")


class DetailRequest(BaseModel):
    """详情请求模型"""
    document_id: str = Field(description="文献ID，如：DOC001")


class APIResponse(BaseModel):
    """API 响应模型"""
    success: bool
    data: Optional[str] = None
    error: Optional[str] = None


# 创建 FastAPI 应用
app = FastAPI(
    title="Dify MCP 桥接服务",
    description="将 FastMCP 图书馆服务器包装为 HTTP API，供 Dify 调用",
    version="1.0.0"
)

# 添加 CORS 支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """根路径 - 服务信息"""
    return {
        "service": "Dify MCP 桥接服务",
        "version": "1.0.0",
        "description": "将 FastMCP 图书馆服务器包装为 HTTP API",
        "endpoints": {
            "search": "POST /search - 搜索文献",
            "detail": "POST /detail - 获取文献详情",
            "search_get": "GET /search/{query} - 简化搜索接口",
            "detail_get": "GET /detail/{document_id} - 简化详情接口"
        },
        "dify_tools": [
            {
                "name": "search_documents",
                "url": "http://*************:8082/search",
                "method": "POST"
            },
            {
                "name": "get_document_detail",
                "url": "http://*************:8082/detail",
                "method": "POST"
            }
        ],
        "network_info": {
            "local_ip": "*************",
            "network": "************/23",
            "port": 8082
        }
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 测试认证是否正常
        token = library_server._ensure_valid_token()
        return {
            "status": "healthy",
            "mcp_server": "connected",
            "auth": "valid" if token else "invalid"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


@app.post("/search", response_model=APIResponse)
async def search_documents_api(request: SearchRequest):
    """
    搜索文献 API

    供 Dify 调用的文献搜索接口
    """
    try:
        # 确保有有效令牌
        library_server._ensure_valid_token()

        # 直接调用业务逻辑
        from app.models.document import DocumentSearchRequest

        search_request = DocumentSearchRequest(
            Rule=request.query,
            PageSize=request.page_size,
            PageIndex=request.page_index
        )

        result = library_server.document_service.search_documents(search_request)

        # 格式化结果
        documents_text = f"📚 检索到 {result.Total} 条文献记录（第 {result.PageIndex}/{result.TotalPages} 页）\n\n"

        for i, doc in enumerate(result.Documents, 1):
            documents_text += f"{i}. **{doc.Title}**\n"
            documents_text += f"   🆔 ID: {doc.Identifier}\n"
            if doc.Author:
                documents_text += f"   👤 作者: {doc.Author}\n"
            if doc.Publisher:
                documents_text += f"   🏢 出版社: {doc.Publisher}\n"
            if doc.PublishYear:
                documents_text += f"   📅 出版年份: {doc.PublishYear}\n"
            if doc.Subject:
                documents_text += f"   🏷️ 主题: {doc.Subject}\n"
            if doc.CallNumber:
                documents_text += f"   📖 索书号: {doc.CallNumber}\n"
            documents_text += "\n"

        if result.Total > len(result.Documents):
            documents_text += f"💡 提示: 还有更多结果，可以使用 page_index={result.PageIndex + 1} 查看下一页"

        return APIResponse(success=True, data=documents_text)

    except Exception as e:
        return APIResponse(success=False, error=str(e))


@app.post("/detail", response_model=APIResponse)
async def get_document_detail_api(request: DetailRequest):
    """
    获取文献详情 API

    供 Dify 调用的文献详情接口
    """
    try:
        # 确保有有效令牌
        library_server._ensure_valid_token()

        # 直接调用业务逻辑
        detail_result = library_server.document_service.get_document_detail(request.document_id)
        doc = detail_result.Document

        # 格式化详情
        detail_text = f"📖 **文献详细信息**\n\n"
        detail_text += f"**📋 标题**: {doc.Title}\n"
        detail_text += f"**🆔 ID**: {doc.Identifier}\n"

        if doc.Author:
            detail_text += f"**👤 作者**: {doc.Author}\n"
        if doc.Publisher:
            detail_text += f"**🏢 出版社**: {doc.Publisher}\n"
        if doc.PublishYear:
            detail_text += f"**📅 出版年份**: {doc.PublishYear}\n"
        if doc.PublishPlace:
            detail_text += f"**🌍 出版地**: {doc.PublishPlace}\n"
        if doc.ISBN:
            detail_text += f"**📚 ISBN**: {doc.ISBN}\n"
        if doc.Pages:
            detail_text += f"**📄 页数**: {doc.Pages}\n"
        if doc.Language:
            detail_text += f"**🌐 语言**: {doc.Language}\n"
        if doc.Subject:
            detail_text += f"**🏷️ 主题**: {doc.Subject}\n"
        if doc.CallNumber:
            detail_text += f"**📖 索书号**: {doc.CallNumber}\n"

        if doc.Summary:
            detail_text += f"\n**📝 摘要**: {doc.Summary}\n"

        if doc.Keywords:
            detail_text += f"\n**🔑 关键词**: {', '.join(doc.Keywords)}\n"

        if doc.Location:
            detail_text += f"\n**📍 馆藏位置**: {doc.Location}\n"
        if doc.Status:
            detail_text += f"**📊 借阅状态**: {doc.Status}\n"

        return APIResponse(success=True, data=detail_text)

    except Exception as e:
        return APIResponse(success=False, error=str(e))


@app.get("/search/{query}")
async def search_documents_get(query: str, page_size: int = 10, page_index: int = 1):
    """
    简化的搜索接口 (GET)

    方便测试和简单调用
    """
    try:
        # 调用 POST 接口的逻辑
        request = SearchRequest(query=query, page_size=page_size, page_index=page_index)
        result = await search_documents_api(request)
        return {"success": result.success, "data": result.data, "error": result.error}

    except Exception as e:
        return {"success": False, "error": str(e)}


@app.get("/detail/{document_id}")
async def get_document_detail_get(document_id: str):
    """
    简化的详情接口 (GET)

    方便测试和简单调用
    """
    try:
        # 调用 POST 接口的逻辑
        request = DetailRequest(document_id=document_id)
        result = await get_document_detail_api(request)
        return {"success": result.success, "data": result.data, "error": result.error}

    except Exception as e:
        return {"success": False, "error": str(e)}


@app.get("/auth/status")
async def get_auth_status():
    """获取认证状态"""
    try:
        import time
        current_time = time.time()
        
        if (library_server._access_token and 
            library_server._token_expires_at and 
            current_time < library_server._token_expires_at):
            
            remaining_time = library_server._token_expires_at - current_time
            return {
                "authenticated": True,
                "remaining_time": int(remaining_time),
                "auto_refresh": True
            }
        else:
            return {
                "authenticated": False,
                "message": "未认证或已过期，下次调用时将自动获取新令牌"
            }
    except Exception as e:
        return {"error": str(e)}


if __name__ == "__main__":
    import socket
    import subprocess
    import platform

    # 获取本机IP - 优先使用指定的局域网IP
    def get_local_ip():
        # 首先尝试获取指定网段的IP
        try:
            # 在macOS/Linux上使用ifconfig获取网络接口信息
            if platform.system() in ['Darwin', 'Linux']:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                for line in lines:
                    if '172.25.' in line and 'inet ' in line:
                        # 提取IP地址
                        parts = line.strip().split()
                        for i, part in enumerate(parts):
                            if part == 'inet' and i + 1 < len(parts):
                                ip = parts[i + 1]
                                if ip.startswith('172.25.'):
                                    return ip
        except Exception:
            pass

        # 备用方法：通过socket连接获取
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"

    # 手动指定已知的局域网IP
    local_ip = "*************"  # 使用你提供的IP地址
    detected_ip = get_local_ip()
    port = 8082

    print(f"🚀 启动 Dify MCP 桥接服务...")
    print(f"📡 服务地址:")
    print(f"  - 本地访问: http://localhost:{port}")
    print(f"  - 局域网访问: http://{local_ip}:{port}")
    if detected_ip != local_ip and detected_ip != "127.0.0.1":
        print(f"  - 检测到的IP: http://{detected_ip}:{port}")
    print(f"🔧 Dify 工具配置:")
    print(f"  - 搜索工具: http://{local_ip}:{port}/search")
    print(f"  - 详情工具: http://{local_ip}:{port}/detail")
    print(f"  - API 文档: http://{local_ip}:{port}/docs")
    print(f"🌐 局域网段: ************/23")
    print("✅ 服务启动中...")

    uvicorn.run(
        "dify_mcp_bridge:app",
        host="0.0.0.0",  # 监听所有网络接口
        port=port,
        reload=True,
        log_level="info"
    )
