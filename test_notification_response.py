#!/usr/bin/env python3
"""
专门测试通知响应格式
确保不会返回空字符串导致 Dify 的 Pydantic 验证错误
"""

import requests
import json


def test_notification_response_format():
    """测试通知响应格式"""
    print("📢 测试通知响应格式...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送通知请求: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.content)} bytes")
        print(f"📄 Content-Type: {response.headers.get('content-type', 'None')}")
        
        if response.content:
            try:
                response_data = response.json()
                print(f"📋 响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证响应格式
                if (response_data.get('jsonrpc') == '2.0' and 
                    'result' in response_data and
                    response_data.get('result') == {}):
                    print("✅ 通知响应格式正确：标准 JSON-RPC 响应")
                    return True
                else:
                    print("❌ 通知响应格式不正确")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ 响应不是有效的 JSON: {e}")
                print(f"   原始响应: '{response.text}'")
                return False
        else:
            print("❌ 响应为空，这会导致 Dify 的 Pydantic 验证错误")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_notification_with_id():
    """测试带 ID 的通知"""
    print("\n📢 测试带 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {},
        "id": "notification-test"  # 添加 ID
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送带 ID 的通知: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.content:
            try:
                response_data = response.json()
                print(f"📋 响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证 ID 是否正确返回
                if response_data.get('id') == "notification-test":
                    print("✅ ID 正确返回")
                    return True
                else:
                    print(f"❌ ID 不匹配: 期望 'notification-test', 实际 '{response_data.get('id')}'")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ 响应不是有效的 JSON: {e}")
                return False
        else:
            print("❌ 响应为空")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_various_notification_formats():
    """测试各种通知格式"""
    print("\n🧪 测试各种通知格式...")
    
    test_cases = [
        {
            "name": "无 params 的通知",
            "request": {
                "jsonrpc": "2.0",
                "method": "notifications/initialized"
            }
        },
        {
            "name": "空 params 的通知",
            "request": {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": {}
            }
        },
        {
            "name": "null params 的通知",
            "request": {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": None
            }
        },
        {
            "name": "带整数 ID 的通知",
            "request": {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": {},
                "id": 0
            }
        }
    ]
    
    results = []
    for test_case in test_cases:
        print(f"\n📤 {test_case['name']}")
        
        try:
            response = requests.post(
                "http://172.25.234.82:8083/mcp",
                headers={"Content-Type": "application/json"},
                json=test_case['request'],
                timeout=10
            )
            
            if response.status_code == 200 and response.content:
                try:
                    response_data = response.json()
                    if (response_data.get('jsonrpc') == '2.0' and 
                        'result' in response_data):
                        print("✅ 格式正确")
                        results.append(True)
                    else:
                        print("❌ 格式错误")
                        results.append(False)
                except:
                    print("❌ JSON 解析失败")
                    results.append(False)
            else:
                print(f"❌ 请求失败: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append(False)
    
    return all(results)


def main():
    """主测试函数"""
    print("🚀 测试通知响应格式")
    print("🎯 确保不会返回空字符串导致 Dify 验证错误")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("基础通知响应", test_notification_response_format),
        ("带 ID 的通知", test_notification_with_id),
        ("各种通知格式", test_various_notification_formats),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("🏁 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！")
        print("💡 通知响应格式正确，应该不会再出现 Pydantic 验证错误")
        print("🔧 现在可以在 Dify 中安全地配置 MCP 服务了")
    else:
        print("⚠️  部分测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
