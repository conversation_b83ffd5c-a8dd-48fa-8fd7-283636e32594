#!/usr/bin/env python3
"""
FastMCP 服务器测试脚本
测试图书馆文献查询 FastMCP 服务器的功能
"""

import asyncio
import json
from typing import Dict, Any
from fastmcp_server import mcp, library_server


async def test_search_documents():
    """测试文献搜索功能"""
    print("🔍 测试文献搜索功能...")
    
    # 测试用例
    test_cases = [
        {
            "name": "搜索Python相关文献",
            "args": {"query": "(K=Python)", "page_size": 3}
        },
        {
            "name": "搜索作者张三的文献",
            "args": {"query": "(A=张三)", "page_size": 5}
        },
        {
            "name": "组合搜索",
            "args": {"query": "(K=机器学习) AND O=清华大学", "page_size": 2}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   查询: {test_case['args']['query']}")
        print("-" * 50)
        
        try:
            # 直接调用工具函数进行测试
            from fastmcp_server import SearchDocumentsArgs, search_documents
            args = SearchDocumentsArgs(**test_case['args'])
            result = await search_documents(args)
            print(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()


async def test_get_document_detail():
    """测试获取文献详情功能"""
    print("📖 测试获取文献详情功能...")
    
    # 测试用例
    test_cases = [
        {"name": "获取DOC001详情", "document_id": "DOC001"},
        {"name": "获取DOC007详情", "document_id": "DOC007"},
        {"name": "获取不存在的文献", "document_id": "DOC999"}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   文献ID: {test_case['document_id']}")
        print("-" * 50)
        
        try:
            from fastmcp_server import GetDocumentDetailArgs, get_document_detail
            args = GetDocumentDetailArgs(document_id=test_case['document_id'])
            result = await get_document_detail(args)
            print(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()


async def test_resources():
    """测试资源功能"""
    print("📋 测试资源功能...")
    
    try:
        print("\n1. 获取认证状态")
        print("-" * 30)
        from fastmcp_server import get_auth_status
        auth_status = await get_auth_status()
        print(auth_status)
        
        print("\n2. 获取服务器信息")
        print("-" * 30)
        from fastmcp_server import get_server_info
        server_info = await get_server_info()
        print(server_info)
        
    except Exception as e:
        print(f"❌ 资源测试失败: {e}")


async def test_authentication():
    """测试认证功能"""
    print("🔐 测试认证功能...")
    
    try:
        # 测试令牌获取
        token = library_server._ensure_valid_token()
        print(f"✅ 成功获取访问令牌: {token[:20]}...")
        
        # 测试令牌有效性
        import time
        if library_server._token_expires_at:
            remaining = library_server._token_expires_at - time.time()
            print(f"⏰ 令牌剩余时间: {int(remaining)} 秒")
        
    except Exception as e:
        print(f"❌ 认证测试失败: {e}")


def test_mcp_server_info():
    """测试 MCP 服务器基本信息"""
    print("ℹ️  测试 MCP 服务器信息...")
    
    try:
        # 获取服务器基本信息
        print(f"📋 服务器名称: {mcp.name}")
        
        # 获取工具列表
        tools = []
        for name, tool in mcp._tools.items():
            tools.append({
                "name": name,
                "description": tool.__doc__.strip().split('\n')[0] if tool.__doc__ else "无描述"
            })
        
        print(f"🛠️  可用工具数量: {len(tools)}")
        for tool in tools:
            print(f"   - {tool['name']}: {tool['description']}")
        
        # 获取资源列表
        resources = list(mcp._resources.keys())
        print(f"📚 可用资源数量: {len(resources)}")
        for resource in resources:
            print(f"   - {resource}")
            
    except Exception as e:
        print(f"❌ 服务器信息测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始 FastMCP 服务器功能测试")
    print("=" * 60)
    
    # 测试服务器基本信息
    test_mcp_server_info()
    print()
    
    # 测试认证功能
    await test_authentication()
    print()
    
    # 测试资源功能
    await test_resources()
    print()
    
    # 测试工具功能
    await test_search_documents()
    await test_get_document_detail()
    
    print("=" * 60)
    print("🏁 测试完成！")
    print()
    print("💡 使用说明:")
    print("1. 运行 FastMCP 服务器: python fastmcp_server.py")
    print("2. 或使用启动脚本: ./start_fastmcp.sh")
    print("3. 服务器支持标准 MCP 协议，可以被任何 MCP 客户端连接")


if __name__ == "__main__":
    asyncio.run(main())
