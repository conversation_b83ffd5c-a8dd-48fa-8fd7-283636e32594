#!/usr/bin/env python3
"""
测试 Dify MCP 服务器
验证是否符合 Dify 的 MCP 要求
"""

import requests
import json
import time


def test_mcp_initialize():
    """测试 MCP 初始化"""
    print("🔧 测试 MCP 初始化...")
    
    mcp_request = {
        "jsonrpc": "2.0",
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-03-26",
            "capabilities": {},
            "clientInfo": {
                "name": "dify",
                "version": "1.6.0"
            }
        },
        "id": "init-1"
    }
    
    try:
        response = requests.post(
            "http://*************:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=mcp_request,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 初始化成功")
            print(f"   协议版本: {data.get('result', {}).get('protocolVersion')}")
            print(f"   服务器名称: {data.get('result', {}).get('serverInfo', {}).get('name')}")
            return True
        else:
            print(f"❌ 初始化失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 初始化异常: {e}")
        return False


def test_mcp_notifications():
    """测试 MCP 通知"""
    print("\n📢 测试 MCP 通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://*************:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 通知处理成功")
            return True
        else:
            print(f"❌ 通知处理失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 通知异常: {e}")
        return False


def test_mcp_tools_list():
    """测试获取工具列表"""
    print("\n🛠️  测试获取工具列表...")
    
    tools_request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "params": {},
        "id": "tools-1"
    }
    
    try:
        response = requests.post(
            "http://*************:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=tools_request,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            tools = data.get('result', {}).get('tools', [])
            print(f"✅ 获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"   - {tool.get('name')}: {tool.get('description')}")
            return True
        else:
            print(f"❌ 获取工具列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取工具列表异常: {e}")
        return False


def test_mcp_tool_call():
    """测试工具调用"""
    print("\n🔧 测试工具调用...")
    
    # 测试搜索工具
    search_request = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "search_documents",
            "arguments": {
                "query": "(K=Python)",
                "page_size": 3
            }
        },
        "id": "call-1"
    }
    
    try:
        response = requests.post(
            "http://*************:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=search_request,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('result', {}).get('content', [])
            if content:
                text_content = content[0].get('text', '')
                print("✅ 搜索工具调用成功")
                print(f"   结果预览: {text_content[:100]}...")
            else:
                print("❌ 搜索工具返回空内容")
                return False
        else:
            print(f"❌ 搜索工具调用失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 搜索工具调用异常: {e}")
        return False
    
    # 测试详情工具
    detail_request = {
        "jsonrpc": "2.0",
        "method": "tools/call",
        "params": {
            "name": "get_document_detail",
            "arguments": {
                "document_id": "DOC001"
            }
        },
        "id": "call-2"
    }
    
    try:
        response = requests.post(
            "http://*************:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=detail_request,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('result', {}).get('content', [])
            if content:
                text_content = content[0].get('text', '')
                print("✅ 详情工具调用成功")
                print(f"   结果预览: {text_content[:100]}...")
                return True
            else:
                print("❌ 详情工具返回空内容")
                return False
        else:
            print(f"❌ 详情工具调用失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 详情工具调用异常: {e}")
        return False


def test_server_info():
    """测试服务器信息"""
    print("\nℹ️  测试服务器信息...")
    
    try:
        response = requests.get("http://*************:8083/", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务器信息:")
            print(f"   名称: {data.get('name')}")
            print(f"   版本: {data.get('version')}")
            print(f"   协议: {data.get('protocol')}")
            print(f"   传输: {data.get('transport')}")
            print(f"   MCP 端点: {data.get('endpoints', {}).get('mcp')}")
            return True
        else:
            print(f"❌ 获取服务器信息失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取服务器信息异常: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    print("\n🏥 测试健康检查...")
    
    try:
        response = requests.get("http://*************:8083/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康状态: {data.get('status')}")
            print(f"   认证状态: {data.get('auth')}")
            print(f"   可用工具: {data.get('tools')}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 Dify MCP 服务器")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: http://*************:8083")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("服务器信息", test_server_info),
        ("健康检查", test_health_check),
        ("MCP 初始化", test_mcp_initialize),
        ("MCP 通知", test_mcp_notifications),
        ("工具列表", test_mcp_tools_list),
        ("工具调用", test_mcp_tool_call),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("🏁 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！MCP 服务器准备就绪")
        print("\n💡 Dify 配置信息:")
        print("   MCP 服务器 URL: http://*************:8083/mcp")
        print("   协议版本: 2025-03-26")
        print("   传输方式: HTTP")
        print("\n🔧 在 Dify 中添加 MCP 工具提供商:")
        print("   1. 进入工具页面")
        print("   2. 选择 MCP")
        print("   3. 输入服务器 URL: http://*************:8083/mcp")
        print("   4. 输入显示名称: 图书馆文献查询")
        print("   5. 输入服务器标识符: library-search")
    else:
        print("⚠️  部分测试失败，请检查服务状态")
        print("💡 确保 MCP 服务器正在运行: python dify_mcp_server.py")


if __name__ == "__main__":
    main()
