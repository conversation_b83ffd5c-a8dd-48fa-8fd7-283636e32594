# Dify 集成指南 - MCP 桥接方案

## 🎯 方案概述

由于 Dify 目前对 MCP 协议的支持有限，我们创建了一个 HTTP 桥接服务，将 FastMCP 服务器包装成 HTTP API，这样就可以在 Dify 中作为自定义工具使用。

## 🏗️ 架构图

```
Dify → HTTP API → MCP 桥接服务 → FastMCP 服务器 → 图书馆业务逻辑
```

## 🚀 快速开始

### 1. 启动桥接服务

```bash
# 确保在项目目录
cd /Users/<USER>/src/HustLibraryDemo

# 激活虚拟环境
source venv/bin/activate

# 启动桥接服务
python dify_mcp_bridge.py
```

服务启动后会显示：
```
🚀 启动 Dify MCP 桥接服务...
📡 服务地址:
  - 本地访问: http://localhost:8082
  - 局域网访问: http://*************:8082
🔧 Dify 工具配置:
  - 搜索工具: http://*************:8082/search
  - 详情工具: http://*************:8082/detail
  - API 文档: http://*************:8082/docs
🌐 局域网段: ************/23
```

### 2. 验证服务

访问 http://*************:8082/docs 查看 API 文档，或运行：

```bash
# 网络访问测试
python test_network_access.py

# 健康检查
curl http://*************:8082/health

# 测试搜索
curl -X POST http://*************:8082/search \
  -H "Content-Type: application/json" \
  -d '{"query": "(K=Python)", "page_size": 3}'
```

## 🔧 在 Dify 中配置工具

### 工具一：文献搜索

1. **进入 Dify 工具配置**
2. **添加自定义工具**
3. **配置参数**：

- **工具名称**: `search_documents`
- **工具描述**: `搜索图书馆文献，支持关键词、作者、标题等条件搜索`
- **HTTP 方法**: `POST`
- **URL**: `http://*************:8082/search`
- **请求头**: 
  ```json
  {
    "Content-Type": "application/json"
  }
  ```
- **请求体**:
  ```json
  {
    "query": "{{query}}",
    "page_size": {{page_size}},
    "page_index": {{page_index}}
  }
  ```

**参数定义**:
- `query` (string, 必需): 检索表达式
- `page_size` (integer, 可选, 默认10): 每页数量
- `page_index` (integer, 可选, 默认1): 页码

### 工具二：文献详情

1. **添加第二个自定义工具**
2. **配置参数**：

- **工具名称**: `get_document_detail`
- **工具描述**: `根据文献ID获取详细信息，包括摘要、关键词、馆藏位置等`
- **HTTP 方法**: `POST`
- **URL**: `http://[你的IP]:8082/detail`
- **请求头**: 
  ```json
  {
    "Content-Type": "application/json"
  }
  ```
- **请求体**:
  ```json
  {
    "document_id": "{{document_id}}"
  }
  ```

**参数定义**:
- `document_id` (string, 必需): 文献的唯一标识符

## 📝 检索表达式语法

支持以下格式的检索表达式：

- `(K=关键词)` - 按关键词搜索
- `(A=作者名)` - 按作者搜索  
- `(T=标题)` - 按标题搜索
- `(O=机构名)` - 按出版机构搜索
- `(K=关键词) AND O=机构` - 组合搜索
- `(K=关键词) OR A=作者` - 或条件搜索

## 🎯 在 Dify 中使用

### Agent 应用示例

1. **创建 Agent 应用**
2. **配置系统提示词**：

```
你是一个图书馆查询助手，可以帮助用户搜索和查看文献信息。

你有两个工具可以使用：
1. search_documents - 搜索文献，支持多种检索条件
2. get_document_detail - 获取文献的详细信息

使用指南：
- 搜索时使用合适的检索表达式，如 (K=Python) 搜索Python相关文献
- 可以组合多个条件，如 (K=机器学习) AND O=清华大学
- 获取详情时使用搜索结果中的文献ID

请根据用户需求智能选择和使用工具。
```

3. **绑定工具**：选择刚才配置的两个自定义工具

### Workflow 应用示例

1. **创建 Workflow 应用**
2. **添加节点**：
   - **开始节点**：接收用户查询
   - **HTTP 请求节点**：调用搜索 API
   - **条件判断节点**：检查搜索结果
   - **HTTP 请求节点**：获取详情（如需要）
   - **结束节点**：返回结果

## 🧪 测试示例

### 测试对话示例

**用户**: "搜索Python编程相关的书籍"

**AI 响应**: 
```
我来帮你搜索Python编程相关的书籍。

[调用 search_documents 工具]
- query: "(K=Python)"
- page_size: 5

搜索结果：
📚 检索到 1 条文献记录（第 1/1 页）

1. **Python编程：从入门到实践**
   🆔 ID: DOC002
   👤 作者: Eric Matthes
   🏢 出版社: 人民邮电出版社
   📅 出版年份: 2016
```

**用户**: "获取第一本书的详细信息"

**AI 响应**:
```
[调用 get_document_detail 工具]
- document_id: "DOC002"

📖 **文献详细信息**

**📋 标题**: Python编程：从入门到实践
**🆔 ID**: DOC002
**👤 作者**: Eric Matthes
**🏢 出版社**: 人民邮电出版社
**📅 出版年份**: 2016
...
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查桥接服务是否运行: `curl http://localhost:8082/health`
   - 确认 IP 地址和端口正确
   - 检查防火墙设置

2. **工具调用失败**
   - 检查 Dify 中的 URL 配置
   - 验证请求体格式
   - 查看桥接服务日志

3. **搜索无结果**
   - 检查检索表达式语法
   - 确认文献ID格式正确
   - 验证认证状态: `curl http://localhost:8082/auth/status`

### 调试方法

1. **查看服务日志**：桥接服务会显示详细的请求日志
2. **测试 API**：使用 curl 或 Postman 直接测试接口
3. **检查认证**：访问 `/auth/status` 端点查看认证状态

## 🚀 高级配置

### 自定义端口

修改 `dify_mcp_bridge.py` 中的端口设置：

```python
port = 8082  # 改为你想要的端口
```

### 添加认证

如需要，可以在桥接服务中添加 API 密钥认证：

```python
from fastapi import Header

async def verify_api_key(x_api_key: str = Header()):
    if x_api_key != "your-secret-key":
        raise HTTPException(status_code=401, detail="Invalid API Key")
```

### 日志配置

启用详细日志：

```bash
export LOG_LEVEL=DEBUG
python dify_mcp_bridge.py
```

## 🎉 总结

通过这个桥接方案，你可以：

1. ✅ 在 Dify 中使用图书馆文献查询功能
2. ✅ 保持 FastMCP 的所有优势
3. ✅ 获得完整的 HTTP API 支持
4. ✅ 享受自动认证管理
5. ✅ 使用标准的 Dify 工具配置

这个方案是当前 Dify 集成 MCP 服务的最佳实践！
