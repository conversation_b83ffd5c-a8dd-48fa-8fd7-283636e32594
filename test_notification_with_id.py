#!/usr/bin/env python3
"""
测试通知响应始终包含 ID
确保所有响应都有 id 字段，满足 Dify 的要求
"""

import requests
import json


def test_notification_without_id():
    """测试没有 ID 的通知"""
    print("📢 测试没有 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {}
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送通知请求: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200 and response.content:
            try:
                response_data = response.json()
                print(f"📋 解析后的响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证响应格式
                if (response_data.get('jsonrpc') == '2.0' and 
                    'result' in response_data and
                    'id' in response_data):  # 确保有 id 字段
                    print(f"✅ 通知响应包含 id: {response_data.get('id')}")
                    return True
                else:
                    print("❌ 响应格式不正确，缺少必要字段")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                return False
        else:
            print("❌ 响应状态或内容不正确")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_notification_with_id():
    """测试带 ID 的通知"""
    print("\n📢 测试带 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {},
        "id": 0  # 使用整数 ID，模拟 Dify 的行为
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送带 ID 的通知: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200 and response.content:
            try:
                response_data = response.json()
                print(f"📋 解析后的响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证 ID 是否正确返回
                if response_data.get('id') == 0:
                    print("✅ ID 正确返回")
                    return True
                else:
                    print(f"❌ ID 不匹配: 期望 0, 实际 '{response_data.get('id')}'")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                return False
        else:
            print("❌ 响应状态或内容不正确")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_notification_string_id():
    """测试字符串 ID 的通知"""
    print("\n📢 测试字符串 ID 的通知...")
    
    notification_request = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized",
        "params": {},
        "id": "test-notification"
    }
    
    try:
        response = requests.post(
            "http://172.25.234.82:8083/mcp",
            headers={"Content-Type": "application/json"},
            json=notification_request,
            timeout=10
        )
        
        print(f"📤 发送字符串 ID 通知: {json.dumps(notification_request, indent=2)}")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200 and response.content:
            try:
                response_data = response.json()
                print(f"📋 解析后的响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证 ID 是否正确返回
                if response_data.get('id') == "test-notification":
                    print("✅ 字符串 ID 正确返回")
                    return True
                else:
                    print(f"❌ ID 不匹配: 期望 'test-notification', 实际 '{response_data.get('id')}'")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                return False
        else:
            print("❌ 响应状态或内容不正确")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_dify_auth_sequence():
    """测试 Dify 认证序列"""
    print("\n🔐 测试 Dify 认证序列...")
    
    # 模拟 Dify 的认证请求序列
    requests_sequence = [
        {
            "name": "初始化",
            "request": {
                "method": "initialize",
                "params": {
                    "protocolVersion": "2025-03-26",
                    "capabilities": {
                        "sampling": {},
                        "roots": {"listChanged": True}
                    },
                    "clientInfo": {"name": "Dify", "version": "1.8.0"}
                },
                "jsonrpc": "2.0",
                "id": 0
            }
        },
        {
            "name": "通知",
            "request": {
                "method": "notifications/initialized",
                "jsonrpc": "2.0"
            }
        },
        {
            "name": "工具列表",
            "request": {
                "method": "tools/list",
                "jsonrpc": "2.0",
                "id": 1
            }
        }
    ]
    
    for i, test_case in enumerate(requests_sequence, 1):
        print(f"{i}️⃣ {test_case['name']}...")
        
        try:
            response = requests.post(
                "http://172.25.234.82:8083/mcp",
                json=test_case['request'],
                timeout=10
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 验证响应格式
                if ('jsonrpc' in response_data and 
                    'id' in response_data and
                    ('result' in response_data or 'error' in response_data)):
                    print(f"✅ {test_case['name']}成功，响应包含必要字段")
                else:
                    print(f"❌ {test_case['name']}响应格式不完整")
                    return False
            else:
                print(f"❌ {test_case['name']}失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ {test_case['name']}异常: {e}")
            return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 测试通知响应始终包含 ID")
    print("🎯 确保所有响应都有 id 字段，满足 Dify 的要求")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("没有 ID 的通知", test_notification_without_id),
        ("带整数 ID 的通知", test_notification_with_id),
        ("带字符串 ID 的通知", test_notification_string_id),
        ("Dify 认证序列", test_dify_auth_sequence),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("🏁 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！")
        print("💡 所有响应都包含 id 字段")
        print("🔧 这应该能彻底解决 Dify 的 JSONRPCResponse.id 验证错误")
        print("\n🚀 现在可以在 Dify 中成功配置 MCP 服务:")
        print("   URL: http://172.25.234.82:8083/mcp")
        print("   显示名称: 图书馆文献查询")
        print("   服务器标识符: library-search")
    else:
        print("⚠️  部分测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
