#!/bin/bash

# 图书馆文献查询 FastMCP 服务器启动脚本

echo "🚀 启动图书馆文献查询 FastMCP 服务器..."
echo "📡 基于 FastMCP 框架"
echo "🔧 支持标准 MCP 协议"
echo ""

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  警告: 未检测到虚拟环境"
    echo "💡 建议先激活虚拟环境: source venv/bin/activate"
    echo ""
fi

# 检查 FastMCP 是否安装
if ! python -c "import fastmcp" 2>/dev/null; then
    echo "❌ FastMCP 未安装"
    echo "💡 请先安装: pip install fastmcp"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 显示使用说明
echo "📋 使用说明:"
echo "1. 服务器将以 STDIO 模式运行"
echo "2. 支持标准 MCP 客户端连接"
echo "3. 可用工具: search_documents, get_document_detail"
echo "4. 可用资源: library://auth/status, library://info"
echo ""

echo "🎯 启动服务器..."
echo "按 Ctrl+C 停止服务"
echo "=" * 50

# 启动 FastMCP 服务器
python fastmcp_server.py
